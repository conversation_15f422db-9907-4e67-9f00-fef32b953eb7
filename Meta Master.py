import tkinter as tk
from tkinter import PhotoImage
from tkinter import filedialog, messagebox
from tkinter import ttk as tkttk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import google.generativeai as genai
from PIL import Image, ImageTk
import re
import os
import json
import threading
import csv
import time
import cairosvg
import xml.etree.ElementTree as ET
import tempfile
import pyttsx3
from PyPDF2 import PdfReader  # Alternative if PyMuPDF is not required
import sys
import firebase_admin
from firebase_admin import credentials, firestore
from datetime import datetime, timedelta
from ttkbootstrap.dialogs import Messagebox
from license_checker import check_license, get_stable_device_id  # Ensure updated get_stable_device_id is imported
import win32com.client
import pythoncom  # Needed for threading COM issues
from google.oauth2 import service_account
from google.auth.transport.requests import AuthorizedSession
import sqlite3
import cv2
import unicodedata  # Add this import to normalize filenames
import subprocess
from collections import OrderedDict  # For ordered cache implementation
import pyperclip
import requests
import base64

generated_titles_in_session = set()
ellipsis = "..." # Used for truncating titles and descriptions

def show_license_input_window():
    """Display a window to input the license key."""
    license_window = ttk.Window(themename="superhero")
    license_window.title("Enter License Key")
    license_window.geometry("400x200")
    license_window.resizable(False, False)

    ttk.Label(license_window, text="Please enter your license key:", font=("Arial", 12)).pack(pady=20)
    license_entry = ttk.Entry(license_window, width=50)
    license_entry.pack(pady=10)

    def save_license_key():
        license_key = license_entry.get().strip()
        if license_key:
            with open(LICENSE_FILE, "w") as f:
                f.write(license_key)
            Messagebox.show_info("License Activated Successfully!", "Success")
            license_window.destroy()  # Close the license input window

    ttk.Button(license_window, text="Submit", command=save_license_key, bootstyle=SUCCESS).pack(pady=10)
    license_window.mainloop()

if not check_license()[0]:
    show_license_input_window()  # Show the License Activation Window
    sys.exit()  # Exit if the license is not valid


APPDATA_DIR = os.path.join(os.getenv("APPDATA"), "MetaMaster")
os.makedirs(APPDATA_DIR, exist_ok=True)

CONFIG_FILE = os.path.join(APPDATA_DIR, "config.json")  # Save config.json in AppData

def load_config():
    try:
        with open(CONFIG_FILE, "r") as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def save_config(config):
    with open(CONFIG_FILE, "w") as f:
        json.dump(config, f)

config = load_config()

# Convert SVG to PNG using cairosvg
def convert_svg_to_png(svg_path, output_path):
    cairosvg.svg2png(url=svg_path, write_to=output_path)

# Convert EPS to PNG using Pillow
def convert_eps_to_png(eps_path, output_path):
    img = Image.open(eps_path)
    if img.mode == "RGBA" or ("A" in img.mode and hasattr(img, 'convert')) or (img.mode == "P" and "transparency" in img.info): 
        background = Image.new("RGB", img.size, (255,255,255))
        img_for_paste = img
        if img.mode != "RGBA": 
            img_for_paste = img.convert("RGBA")
        
        alpha_channel = img_for_paste.split()[-1]
        is_transparent = any(pixel < 255 for pixel in alpha_channel.getdata())

        if is_transparent:
            background.paste(img_for_paste, mask=alpha_channel)
        else: 
            background.paste(img_for_paste.convert("RGB"))
        img = background
    elif img.mode != "RGB": 
        img = img.convert("RGB")
    img.save(output_path, 'PNG')

def convert_ai_to_png_with_illustrator(ai_path, output_png_path):
    illustrator_app = None
    doc = None
    com_initialized_here = False
    try:
        pythoncom.CoInitialize()
        com_initialized_here = True
        illustrator_app = win32com.client.Dispatch("Illustrator.Application")
        if not illustrator_app:
            print("Failed to dispatch Illustrator application.")
            return False
        if not os.path.exists(ai_path):
            print(f"AI file not found: {ai_path}")
            return False
        doc = illustrator_app.Open(ai_path)
        if not doc:
            print(f"Failed to open AI document: {ai_path}")
            return False
        export_options = win32com.client.Dispatch("Illustrator.ExportOptionsPNG24")
        try: export_options.AntiAliasing = True
        except AttributeError: print("Illustrator version might not support AntiAliasing option for PNG.")
        try: export_options.Transparency = True 
        except AttributeError: print("Illustrator version might not support Transparency option for PNG.")
        try: export_options.ArtBoardClipping = True 
        except AttributeError: print("Illustrator version might not support ArtBoardClipping option for PNG.")
        doc.Export(output_png_path, 5, export_options) 
        print(f"Successfully converted '{os.path.basename(ai_path)}' to '{os.path.basename(output_png_path)}' using Illustrator.")
        return True
    except pythoncom.com_error as e:
        print(f"COM Error during AI to PNG conversion with Illustrator: {e}")
        if e.hresult == -2147221005: 
             messagebox.showwarning("Illustrator Not Found", "Adobe Illustrator does not seem to be installed or properly registered. Cannot convert .ai files for preview.")
        return False
    except Exception as e:
        print(f"General error converting AI to PNG with Illustrator for '{ai_path}': {e}")
        return False
    finally:
        if doc:
            try:
                doc.Close(2)  
            except Exception as e:
                print(f"Error closing AI document: {e}")
        if com_initialized_here:
            pythoncom.CoUninitialize()

def extract_svg_metadata(svg_path):
    tree = ET.parse(svg_path)
    root = tree.getroot()
    title = root.find(".//{http://www.w3.org/2000/svg}title")
    desc = root.find(".//{http://www.w3.org/2000/svg}desc")
    metadata = {
        "title": title.text if title is not None else "No title",
        "description": desc.text if desc is not None else "No description"
    }
    return metadata

def extract_eps_metadata(eps_path):
    metadata = {"title": "No title", "description": "No description"}
    try:
        if not os.path.exists(eps_path):
            raise FileNotFoundError(f"File not found: {eps_path}")
        if not os.access(eps_path, os.R_OK):
            raise PermissionError(f"Permission denied: {eps_path}")
        with open(eps_path, "rb") as f:
            reader = PdfReader(f)
            info = reader.metadata
            metadata["title"] = info.get("/Title", "No title")
            metadata["description"] = info.get("/Subject", "No description")
    except FileNotFoundError as e:
        print(f"Error extracting EPS metadata: {e}")
    except PermissionError as e:
        print(f"Error extracting EPS metadata: {e}")
    except Exception as e:
        print(f"Error extracting EPS metadata: {e}")
    return metadata

pause_event = threading.Event()
pause_event.set()
stop_event = threading.Event()

NEGATIVE_KEYWORDS_FILE = os.path.join(os.getenv("APPDATA"), "MetaMaster", "negative_keywords.txt")

def load_negative_keywords():
    if os.path.exists(NEGATIVE_KEYWORDS_FILE):
        with open(NEGATIVE_KEYWORDS_FILE, "r") as f:
            return f.read().strip().split(",")
    return []

def save_negative_keywords():
    new_keywords = negative_words_var.get().strip()
    if new_keywords:
        with open(NEGATIVE_KEYWORDS_FILE, "w") as f:
            f.write(new_keywords)
        messagebox.showinfo("Saved", "Negative keywords updated.")
    else:
        messagebox.showwarning("Warning", "No keywords entered.")

def reset_negative_keywords():
    if os.path.exists(NEGATIVE_KEYWORDS_FILE):
        os.remove(NEGATIVE_KEYWORDS_FILE)
    negative_words_var.set("")
    messagebox.showinfo("Reset", "Negative keywords reset.")

def clean_metadata_text(text, is_title=False):
    cleaned_text = re.sub(r'\*+', '', text)
    words = cleaned_text.split()
    if not words: return ""
    if is_title:
        unique_words = [words[0]]
        for i in range(1, len(words)):
            if words[i].lower() != unique_words[-1].lower():
                unique_words.append(words[i])
        cleaned_text = " ".join(unique_words)
    else:
        cleaned_text = re.sub(r'\b(\w+)(?:\s+\1\b)+', r'\1', cleaned_text, flags=re.IGNORECASE)
    if is_title:
        cleaned_text = cleaned_text.strip()
        if cleaned_text:
            cleaned_text = cleaned_text[0].upper() + cleaned_text[1:]
        cleaned_text = re.sub(r'^[^\w\s]+|[^\w\s]+$', '', cleaned_text)
        cleaned_text = re.sub(r'\s\s+', ' ', cleaned_text).strip()
    return cleaned_text

def add_custom_title_words(title, max_title_chars):
    """Add custom title words to the title based on position setting."""
    custom_title_words = config.get("custom_title_words", "").strip()
    position = config.get("custom_title_words_position", "suitable place")

    if not custom_title_words or not title:
        return title

    # Parse custom words
    custom_words_list = [word.strip() for word in custom_title_words.split(',') if word.strip()]
    if not custom_words_list:
        return title

    # Check if any custom words are already in the title (case-insensitive)
    title_lower = title.lower()
    words_to_add = [word for word in custom_words_list if word.lower() not in title_lower]

    if not words_to_add:
        return title  # All custom words already present

    # Add words based on position
    if position == "at first":
        new_title = " ".join(words_to_add) + " " + title
    elif position == "at the end":
        new_title = title + " " + " ".join(words_to_add)
    else:  # "suitable place" - add at the end but before any trailing descriptors
        # Find a good insertion point (before words like "isolated", "background", etc.)
        title_words = title.split()
        insertion_point = len(title_words)

        # Look for common trailing descriptors
        trailing_descriptors = ['isolated', 'background', 'transparent', 'white', 'black']
        for i, word in enumerate(title_words):
            if word.lower() in trailing_descriptors:
                insertion_point = i
                break

        # Insert custom words at the suitable position
        title_words[insertion_point:insertion_point] = words_to_add
        new_title = " ".join(title_words)

    # Ensure the new title doesn't exceed max length
    if len(new_title) > max_title_chars:
        # Try to fit by truncating the original title
        available_space = max_title_chars - len(" ".join(words_to_add)) - 1
        if available_space > 10:  # Minimum reasonable title length
            if position == "at first":
                truncated_title = title[:available_space].rsplit(' ', 1)[0]
                new_title = " ".join(words_to_add) + " " + truncated_title
            else:
                truncated_title = title[:available_space].rsplit(' ', 1)[0]
                new_title = truncated_title + " " + " ".join(words_to_add)
        else:
            # If can't fit, return original title
            return title

    return new_title.strip()

def add_custom_keywords(keywords):
    """Add custom keywords to the keywords based on position setting."""
    custom_keywords = config.get("custom_keywords", "").strip()
    position = config.get("custom_keywords_position", "suitable place")

    if not custom_keywords:
        return keywords

    # Parse custom keywords
    custom_keywords_list = [kw.strip() for kw in custom_keywords.split(',') if kw.strip()]
    if not custom_keywords_list:
        return keywords

    # Parse existing keywords
    if keywords:
        existing_keywords = [kw.strip() for kw in keywords.split(',') if kw.strip()]
    else:
        existing_keywords = []

    # Check if any custom keywords are already present (case-insensitive)
    existing_keywords_lower = [kw.lower() for kw in existing_keywords]
    keywords_to_add = [kw for kw in custom_keywords_list if kw.lower() not in existing_keywords_lower]

    if not keywords_to_add:
        return keywords  # All custom keywords already present

    # Add keywords based on position
    if position == "at first":
        new_keywords_list = keywords_to_add + existing_keywords
    elif position == "at the end":
        new_keywords_list = existing_keywords + keywords_to_add
    else:  # "suitable place" - add after the first few main keywords
        # Insert after the first 3-5 keywords (or at the end if fewer)
        insertion_point = min(5, len(existing_keywords))
        new_keywords_list = existing_keywords[:insertion_point] + keywords_to_add + existing_keywords[insertion_point:]

    return ", ".join(new_keywords_list)

def is_likely_silhouette(img):
    has_transparency = img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info)
    if has_transparency:
        if img.mode == 'RGBA':
            _, _, _, a = img.split()
            non_transparent_count = sum(1 for p in a.getdata() if p > 0)
            if non_transparent_count == 0:
                return False  
            grayscale = img.convert("L")
            non_transparent_pixels = []
            for i, (p, alpha) in enumerate(zip(grayscale.getdata(), a.getdata())):
                if alpha > 0:  
                    non_transparent_pixels.append(p)
            if not non_transparent_pixels: return False # Avoid division by zero if all pixels are transparent
            dark_pixels = sum(1 for p in non_transparent_pixels if p < 50)  
            dark_ratio = dark_pixels / len(non_transparent_pixels)
            return dark_ratio > 0.8
    grayscale = img.convert("L")
    pixels = list(grayscale.getdata())
    if not pixels: return False # Avoid division by zero if image is empty
    avg_color = sum(pixels) / len(pixels)
    std_dev = (sum((p - avg_color) ** 2 for p in pixels) / len(pixels)) ** 0.5
    return std_dev < 15  

def refine_png_background(image_path, black_threshold=20):
    try:
        img = Image.open(image_path)
        if img.mode != "RGBA":
            img = img.convert("RGBA")
        datas = img.getdata()
        newData = []
        for item in datas:
            if item[3] > 0 and item[0] < black_threshold and item[1] < black_threshold and item[2] < black_threshold:
                newData.append((item[0], item[1], item[2], 0))  
            else:
                newData.append(item)
        img.putdata(newData)
        img.save(image_path, "PNG")
        print(f"Refined PNG background for: {os.path.basename(image_path)}")
        return True
    except Exception as e:
        print(f"Error refining PNG background for {os.path.basename(image_path)}: {e}")
        return False

def generate_safe_filename_from_title(title_str):
    if not title_str: 
        return "untitled"
    text = unicodedata.normalize('NFKD', str(title_str)).encode('ascii', 'ignore').decode('ascii')
    text = re.sub(r'[<>:"/\\|?*\x00-\x1F]', '', text)
    text = re.sub(r'\s+', ' ', text)
    text = text.strip(' .-_')
    if not text or text.isspace() or all(c == '.' for c in text):
        text = "untitled"
    return text[:200]

def process_single_image(image_path, retry_count=0):
    filename = os.path.basename(image_path)
    item_id = None
    def update_tree_item(item_id, values):
        root.after(0, lambda: tree.item(item_id, values=values))

    # Find the tree item for this file
    for item in tree.get_children():
        values = tree.item(item)["values"]
        if values and values[0] == filename:
            item_id = item
            update_tree_item(item_id, (filename, "Processing...", "Processing...", "Processing...", "Processing...")) # Added category placeholder
            break

    # Debug: Print processing start
    print(f"🔄 Starting to process: {filename}")

    # Load API key with better error handling
    try:
        api_key = load_gemini_api_key()
        print(f"📋 API key loaded: {'✅ Yes' if api_key else '❌ No'}")
    except Exception as e:
        print(f"❌ Error loading API key: {e}")
        if item_id:
            update_tree_item(item_id, (filename, "Error: API key load failed", str(e), "", ""))
        return False

    if not api_key:
        print("❌ No API key found")
        if item_id:
            update_tree_item(item_id, (filename, "Error: No API key", "", "", ""))
        root.after(0, lambda: messagebox.showerror("Gemini Error", "No API keys found. Please add at least one API key."))
        return False
    # Configure Gemini API with better error handling
    try:
        print(f"🔧 Configuring Gemini API...")
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel("gemini-2.5-flash-lite")
        print(f"✅ Gemini API configured successfully")
    except Exception as e:
        error_message = str(e)
        print(f"❌ Gemini API configuration failed: {error_message}")

        if "429" in error_message and retry_count < 3:
            print(f"🔄 Rate limit detected, trying to switch API key (attempt {retry_count + 1}/3)")
            new_key = switch_to_next_api_key()
            if new_key:
                if item_id:
                    update_tree_item(item_id, (filename, "Retrying with different API key...", "", "", "Retrying..."))
                root.after(0, lambda: update_api_key_status())
                return process_single_image(image_path, retry_count + 1)

        if item_id:
            update_tree_item(item_id, (filename, "Error: API Setup Failed", error_message, "", ""))
        root.after(0, lambda: messagebox.showerror("Gemini Setup Failed", f"API Error: {error_message}"))
        return False
    if image_path.lower().endswith(('.mp4', '.mov', '.avi', '.mkv')):
        try:
            with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp_img:
                temp_image_path = tmp_img.name
            frame_path = extract_frame_from_video(image_path, temp_image_path, time_seconds=3)
            if not frame_path:
                raise ValueError("Failed to extract frame from video")
            return process_video_frame_for_metadata(frame_path, image_path) # This function will also need category handling
        except Exception as e:
            error_message = str(e)
            error_type = "Error"
            if "429" in error_message:
                error_type = "Error: Rate Limit (429)"
            if item_id:
                tree.item(item_id, values=(filename, error_type, error_message, "", "Error", "☆☆☆☆☆"))
            else:
                tree.insert("", "end", values=(filename, error_type, error_message, "", "Error", "☆☆☆☆☆"))
            print(f"Error processing video {image_path}: {e}")
            return False
    temp_image_path = None  
    try:
        if image_path.lower().endswith('.svg'):
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_png:
                temp_image_path = tmp_png.name
            try:
                cairosvg.svg2png(url=image_path, write_to=temp_image_path)
            except Exception as e:
                if item_id: update_tree_item(item_id, (filename, "Error: SVG Conversion", str(e), "", ""))
                if os.path.exists(temp_image_path): 
                    try: os.remove(temp_image_path) 
                    except Exception: pass
                return False
        elif image_path.lower().endswith('.ai'): 
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_png:
                temp_image_path = tmp_png.name
            ai_conversion_success = False
            illustrator_error_detail = "AI to PNG conversion with Illustrator failed or Illustrator not found."
            try:
                ai_conversion_success = convert_ai_to_png_with_illustrator(image_path, temp_image_path)
                if not ai_conversion_success:
                    print(f"convert_ai_to_png_with_illustrator returned False for {filename}")
            except Exception as e_ai_conv: 
                illustrator_error_detail = f"Exception during Illustrator AI conversion: {str(e_ai_conv)}"
                print(f"{illustrator_error_detail} for {filename}")
                ai_conversion_success = False 
            if not ai_conversion_success:
                if item_id: update_tree_item(item_id, (filename, "Error: AI Conversion", illustrator_error_detail[:100], "", "")) 
                if os.path.exists(temp_image_path):
                    try: os.remove(temp_image_path)
                    except Exception: pass
                return False
        elif image_path.lower().endswith('.eps'):
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_png:
                temp_image_path = tmp_png.name
            try:
                convert_eps_to_png(image_path, temp_image_path)
            except Exception as e:
                error_msg = f"Error: EPS Conv. ({str(e)[:50]}...)"
                if "Ghostscript" in str(e) or "gsdll" in str(e).lower():
                     error_msg = "Error: EPS (Ghostscript missing/config?)"
                elif "cannot identify image file" in str(e):
                    error_msg = "Error: EPS (Cannot identify file)"
                if item_id: update_tree_item(item_id, (filename, error_msg, str(e), "", ""))
                if os.path.exists(temp_image_path):
                    try: os.remove(temp_image_path)
                    except Exception: pass
                return False
        else:
            with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp_img:
                temp_image_path = tmp_img.name
            try:
                with Image.open(image_path) as img:
                    if img.mode == "RGBA":
                        img = img.convert("RGB")
                    img.thumbnail((800, 800))  
                    img.save(temp_image_path, format="JPEG")
            except Exception as e:
                if item_id: update_tree_item(item_id, (filename, "Error: Image Processing", str(e), "", ""))
                if os.path.exists(temp_image_path):
                    try: os.remove(temp_image_path)
                    except Exception: pass
                return False
        if not temp_image_path or not os.path.exists(temp_image_path):
            if item_id: update_tree_item(item_id, (filename, "Error: Temp File Missing", "", "", ""))
            return False
        
        with Image.open(temp_image_path) as temp_img:
            overall_min_title_chars = min_title_words_var.get() 
            overall_max_title_chars = max_title_words_var.get() 
            png_suffix_phrase = "isolated on a transparent background"
            
            gemini_target_min_len_for_base = overall_min_title_chars 
            gemini_target_max_len_for_base = overall_max_title_chars
            additional_guidance_for_png = ""

            if image_path.lower().endswith('.png') and png_isolated_enabled.get():
                suffix_full_phrase_with_space = " " + png_suffix_phrase
                len_suffix_full_phrase_with_space = len(suffix_full_phrase_with_space)
                gemini_target_max_len_for_base = overall_max_title_chars - len_suffix_full_phrase_with_space
                gemini_target_min_len_for_base = overall_min_title_chars - len_suffix_full_phrase_with_space
                min_reasonable_base_threshold = max(15, overall_min_title_chars // 3)
                if gemini_target_min_len_for_base < min_reasonable_base_threshold:
                    gemini_target_min_len_for_base = min_reasonable_base_threshold
                if gemini_target_max_len_for_base < gemini_target_min_len_for_base:
                    gemini_target_max_len_for_base = gemini_target_min_len_for_base 
                if gemini_target_max_len_for_base <= 0: 
                    gemini_target_max_len_for_base = max(5, gemini_target_min_len_for_base) 
                    additional_guidance_for_png = (
                        f" IMPORTANT: The phrase '{suffix_full_phrase_with_space.strip()}' will be appended to your title. "
                        f"The total title length is very constrained. Please provide a very short base title (target {gemini_target_min_len_for_base}-{gemini_target_max_len_for_base} characters if possible)."
                    )
                else:
                    additional_guidance_for_png = (
                        f" IMPORTANT: The phrase '{suffix_full_phrase_with_space.strip()}' will be appended to your generated title by the program. "
                        f"Therefore, please craft your base title to be between {gemini_target_min_len_for_base} and {gemini_target_max_len_for_base} characters. "
                        f"This base title, when combined with the suffix, should fit within the overall limit of {overall_max_title_chars} characters."
                    )
            
            title_prompt_instruction = (
                f"Title: Create a specific, descriptive title that captures exactly what is shown in the image. Focus on the main subject, objects, actions, or concepts visible. Avoid generic terms like 'professional', 'high-quality', 'modern', 'creative', 'design', 'illustration', 'artwork' unless they specifically describe the visual style. "
                f"Target length for your generated (base) title: {gemini_target_min_len_for_base}-{gemini_target_max_len_for_base} characters. "
                f"{additional_guidance_for_png} "
                "The title MUST NOT contain any punctuation (e.g., colons, hyphens, pipes, exclamation marks). "
                "Be specific about colors, objects, actions, settings, and visual elements. Use concrete nouns and descriptive adjectives that relate to the actual content."
            )

            # Extract filename hint for better content understanding
            filename_hint = os.path.basename(image_path).replace("-", " ").replace("_", " ").split(".")[0]

            prompt_parts = [
                "You are an expert at analyzing images and creating accurate, content-focused metadata for microstock platforms. Your goal is to describe exactly what you see in the image.",
                f"Analyze this image and create metadata that accurately reflects its visual content. Use the filename hint '{filename_hint}' to help understand the subject matter, but focus primarily on what you actually observe in the image.",
                title_prompt_instruction,
                f"Keywords: Generate {min_keywords_var.get()}-{max_keywords_var.get()} specific and relevant keywords based on what you see. Include: objects, colors, actions, settings, styles, moods, and potential uses. Avoid generic terms like 'professional', 'high-quality', 'modern', 'creative' unless they specifically describe the visual style. Focus on searchable terms that buyers would use to find this specific type of content.",
            ]
            if description_enabled.get():
                prompt_parts.append(f"Description: Write a {min_description_var.get()}-{max_description_var.get()} character description that focuses on the visual elements you can observe: colors, objects, composition, setting, style, and mood. Be specific about what you see rather than using marketing language. Avoid generic phrases like 'This image shows', 'perfect for', 'ideal for', 'high-quality', or 'professional'. Instead, describe the actual content, colors, textures, and visual characteristics that make this image unique.")
            else:
                prompt_parts.append("Description: Not requested for this image.")
            
            prompt_parts.extend([
                "Shutterstock Category: Select the single most relevant and commercially viable category for this image from the following list: Animals/Wildlife, Backgrounds/Textures, Beauty/Fashion, Buildings/Landmarks, Business/Finance, Celebrities, Education, Food and Drink, Healthcare/Medical, Holidays, Industrial, Interiors, Miscellaneous, Nature, Objects, People, Religion, Science, Signs/Symbols, Sports/Recreation, Technology, Transportation, Vintage/Retro. Return only the category name.",
                "IMPORTANT: Focus on describing the actual visual content you see in the image. Avoid generic marketing terms like 'professional', 'high-quality', 'modern', 'creative', 'premium', 'elegant', 'stunning', 'beautiful', 'amazing', 'perfect'. Instead, use specific descriptive terms about objects, colors, actions, settings, textures, patterns, and styles that are actually visible. Only use industry terms like 'vector', 'illustration', 'graphic' if they accurately describe the visual style of the content.",
            ])

            if single_word_keywords_var.get():
                prompt_parts.append("IMPORTANT: All generated keywords MUST be single words. Do not use multi-word phrases as keywords.")
            
            prompt_parts.append("Strictly format your response as:\nTitle: [Generated Title]\nKeywords: [comma-separated keywords]\nDescription: [Generated Description or 'Not requested']\nCategory: [Generated Shutterstock Category]")
            prompt_parts.append(temp_img)
            custom_words = custom_words_var.get().strip()
            if custom_words:
                prompt_parts.insert(-2, f"Include these custom words in the metadata: {custom_words}")

            # Add custom prompt parts if configured
            custom_prompt_parts = config.get("custom_prompt_parts", "").strip()
            if custom_prompt_parts:
                prompt_parts.insert(-2, f"Additional instructions: {custom_prompt_parts}")

            is_silhouette = is_likely_silhouette(temp_img)
            has_transparency = temp_img.mode in ('RGBA', 'LA') or (temp_img.mode == 'P' and 'transparency' in temp_img.info)
            
            if image_path.lower().endswith('.png') and png_refine_transparent_enabled.get():
                prompt_parts.insert(0, "CRITICAL INSTRUCTION FOR PNG: ABSOLUTELY DO NOT generate any metadata (title, keywords, or description) containing the phrases: 'Black Background', 'isolated on a Black Background', 'Against Black Background', or 'Isolated Against a Black Background'. Focus on transparent or white backgrounds if applicable.")

            if is_silhouette:
                use_filename_hint = filename_hint_enabled.get()
                if has_transparency:
                    prompt_parts.insert(0, "This is a silhouette design with transparency. Describe it as being 'on a white background' or 'with transparent background', NEVER 'on a black background'.")
                    if use_filename_hint:
                        prompt_parts.insert(1, f"Use the filename hint '{filename_hint}' to understand what this silhouette represents. Focus on the specific subject matter indicated by the filename.")
                        prompt_parts.insert(2, "Create a title that describes the actual subject of the silhouette (person, animal, object, symbol, etc.) rather than using generic terms.")
                    else:
                        prompt_parts.insert(1, "Analyze the silhouette shape to determine what it represents - could be a person, animal, object, symbol, or decorative element.")
                        prompt_parts.insert(2, "Create a specific title based on the recognizable shape or form, avoiding generic terms like 'silhouette design' or 'cutout design'.")
                else:
                    prompt_parts.insert(0, "This is a silhouette design. Focus on identifying what the silhouette represents rather than using generic descriptive terms.")
                    if use_filename_hint:
                        prompt_parts.insert(1, f"Use the filename hint '{filename_hint}' to understand the subject matter of this silhouette.")
            elif has_transparency:
                prompt_parts.insert(0, "This image has transparency. Describe it as being 'on a white background' or 'with transparent background', NEVER 'on a black background'.")
                prompt_parts.insert(1, "Focus on describing the main subject and its visual characteristics rather than the technical aspects of transparency.")
                prompt_parts.insert(2, "Include keywords related to 'transparent', 'isolated', 'cutout' only if they add value to the content description.")

            # Debug: Print before API call
            print(f"🚀 Making API call to Gemini for: {filename}")
            print(f"📝 Prompt parts count: {len(prompt_parts)}")

            try:
                response = model.generate_content(
                    prompt_parts,
                    generation_config={
                        "temperature": 0.3,
                        "max_output_tokens": max_description_var.get() + 100,
                        "top_p": 0.95,
                    }
                )
                print(f"✅ API call successful for: {filename}")
                print(f"📄 Response length: {len(response.text) if response.text else 0} characters")
            except Exception as api_error:
                print(f"❌ API call failed for {filename}: {api_error}")
                raise api_error
        metadata = response.text.split('\n')
        title = ""
        keywords = ""
        description = "" # Will hold the full description
        shutterstock_category = "Miscellaneous" # Default category

        for line in metadata:
            if line.startswith("Title: "):
                raw_title_from_gemini = line.replace("Title: ", "").strip()
                spaced_title = re.sub(r'(?<!^)(?=[A-Z])', ' ', raw_title_from_gemini)
                title = clean_metadata_text(spaced_title, is_title=True)
            elif line.startswith("Keywords: "):
                keywords = clean_metadata_text(line.replace("Keywords: ", "").strip())
            elif line.startswith("Description: "):
                desc_text = line.replace("Description: ", "").strip()
                if desc_text.lower() != "not requested":
                    description = clean_metadata_text(desc_text)
            elif line.startswith("Category: "):
                shutterstock_category = line.replace("Category: ", "").strip()
        
        # Ensure title is cleaned even if not found in specific "Title:" line (fallback)
        if not title and metadata: # If title is still empty but metadata was returned
            potential_title_line = metadata[0] # Assume first line might be the title
            if not potential_title_line.lower().startswith(("keywords:", "description:", "category:")):
                 title = clean_metadata_text(potential_title_line.strip(), is_title=True)

        title = clean_metadata_text(title, is_title=True) # Final clean of base title
        keywords = clean_metadata_text(keywords, is_title=False)

        # Add custom title words and keywords
        title = add_custom_title_words(title, max_title_words_var.get())
        keywords = add_custom_keywords(keywords)

        # 'description' variable now holds the full description if generated, or "" if not.
        # 'shutterstock_category' holds the category.

        # --- Apply Negative Keywords ---
        loaded_negative_keywords = [kw.strip().lower() for kw in negative_words_var.get().strip().split(',') if kw.strip()]
        if loaded_negative_keywords:
            # Filter Title
            if title:
                title_words = title.split()
                title = " ".join([word for word in title_words if word.lower() not in loaded_negative_keywords])
                title = clean_metadata_text(title, is_title=True) # Re-clean after removal

            # Filter Keywords
            if keywords:
                keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]
                filtered_keyword_list = [kw for kw in keyword_list if kw.lower() not in loaded_negative_keywords]
                keywords = ", ".join(filtered_keyword_list)
                keywords = clean_metadata_text(keywords, is_title=False) # Re-clean

            # Filter Description
            if description:
                # For description, remove whole words case-insensitively
                temp_description = description
                for neg_kw in loaded_negative_keywords:
                    # Use regex to remove whole word, case-insensitive
                    temp_description = re.sub(r'\b' + re.escape(neg_kw) + r'\b', '', temp_description, flags=re.IGNORECASE)
                description = clean_metadata_text(temp_description.strip()) # Re-clean and strip extra spaces

        # Clean black background phrases if refine PNG BG is on
        if image_path.lower().endswith('.png') and png_refine_transparent_enabled.get():
            black_background_phrases_to_remove = [
                "Black Background",
                "isolated on a Black Background",
                "Against Black Background",
                "Isolated Against a Black Background"
            ]
            for phrase_to_remove in black_background_phrases_to_remove:
                if title: 
                    title = re.sub(r'(?i)\b' + re.escape(phrase_to_remove) + r'\b', '', title).strip()
                if keywords: 
                    keywords = re.sub(r'(?i)\b' + re.escape(phrase_to_remove) + r'\b', '', keywords).strip()
                if description: # Changed from description_short
                    description = re.sub(r'(?i)\b' + re.escape(phrase_to_remove) + r'\b', '', description).strip()
            
            if title:
                title = re.sub(r'\s\s+', ' ', title).strip()
                title = clean_metadata_text(title, is_title=True)
            if keywords:
                keywords = re.sub(r'\s\s+', ' ', keywords).strip()
                keywords = re.sub(r',,+', ',', keywords).strip().strip(',')
                keywords = clean_metadata_text(keywords, is_title=False)
            if description: # Changed from description_short
                description = re.sub(r'\s\s+', ' ', description).strip()
                description = clean_metadata_text(description)

        if single_word_keywords_var.get() and keywords:
            all_single_words = []
            for kw_phrase in keywords.split(','):
                all_single_words.extend(kw_phrase.strip().split())
            keywords = ", ".join(list(OrderedDict.fromkeys(word for word in all_single_words if word)))

        # --- Refined Title Processing ---
        min_title_chars = overall_min_title_chars # Use overall limits from earlier
        max_title_chars = overall_max_title_chars 
        # png_isolated_phrase and ellipsis are already defined
        
        # Helper for smart truncation (defined within process_single_image)
        def smart_truncate_title(text, max_len, current_ellipsis=""):
            text = str(text).strip() 
            if len(text) <= max_len:
                return text
            if max_len <= len(current_ellipsis): 
                return text[:max_len].strip() 
            
            truncated_text = text[:max_len - len(current_ellipsis)].strip()
            last_space = truncated_text.rfind(' ')
            if last_space != -1 and (len(truncated_text[:last_space]) > max_len / 3 or len(truncated_text[:last_space]) > 3) : 
                 return truncated_text[:last_space].strip() + current_ellipsis
            return truncated_text + current_ellipsis

        # 1. `title` variable holds the base title from Gemini (already cleaned by clean_metadata_text)
        current_processed_title = title 
        
        # 2. Conditionally add PNG suffix
        png_isolated_phrase = "isolated on a transparent background"  # Define the phrase here
        png_suffix_was_added_in_this_step = False
        if image_path.lower().endswith('.png') and png_isolated_enabled.get():
            if not (png_isolated_phrase.lower() in current_processed_title.lower() or "isolated on a white background" in current_processed_title.lower()):
                full_suffix_to_add = " " + png_isolated_phrase
                # Check if adding the suffix would exceed max length
                if len(current_processed_title) + len(full_suffix_to_add) > max_title_chars:
                    # Space available for base title before suffix
                    available_for_base = max_title_chars - len(full_suffix_to_add)
                    if available_for_base > len(ellipsis) + 3: # Min reasonable space for some base + ellipsis
                        current_processed_title = smart_truncate_title(current_processed_title, available_for_base, ellipsis)
                        current_processed_title = (current_processed_title + full_suffix_to_add).strip()
                        png_suffix_was_added_in_this_step = True
                    else: # Not enough space for a meaningful base, prioritize the phrase
                        current_processed_title = smart_truncate_title(png_isolated_phrase, max_title_chars, ellipsis)
                        png_suffix_was_added_in_this_step = True 
                else: # Fits fine
                    current_processed_title = (current_processed_title + full_suffix_to_add).strip()
                    png_suffix_was_added_in_this_step = True
        
        # Add to description as well
        if image_path.lower().endswith('.png') and png_isolated_enabled.get():
            if description and png_isolated_phrase.lower() not in description.lower() and "isolated on a white background" not in description.lower(): # Changed from description_short
                 description = f"{description} {png_isolated_phrase.capitalize()}." # Changed from description_short
            elif not description and description_enabled.get(): # Changed from description_short
                 description = f"{png_isolated_phrase.capitalize()}." # Changed from description_short

        # 3. Ensure title meets minimum character length by expanding with relevant descriptive terms
        if len(current_processed_title) < min_title_chars:
            # Extract more descriptive terms from keywords if available
            descriptive_terms = []
            if keywords:
                keyword_list = [kw.strip() for kw in keywords.split(',')[:5]]  # Use first 5 keywords
                # Filter out generic terms and keep specific descriptive ones
                for kw in keyword_list:
                    if (len(kw) > 3 and
                        kw.lower() not in ['professional', 'high', 'quality', 'modern', 'creative', 'design', 'premium', 'elegant'] and
                        kw.lower() not in current_processed_title.lower()):
                        descriptive_terms.append(kw)

            # If no good keywords, use minimal generic terms as last resort
            if not descriptive_terms:
                file_ext = os.path.splitext(image_path)[1].lower()
                if file_ext in ['.svg', '.eps', '.ai']:
                    descriptive_terms = ["vector"]
                elif file_ext in ['.jpg', '.jpeg', '.png']:
                    descriptive_terms = ["image"]

            temp_title_parts = current_processed_title.split()
            original_title_words = {word.lower() for word in temp_title_parts}

            for term in descriptive_terms[:2]:  # Limit to 2 additional terms
                if len(current_processed_title) >= min_title_chars: break
                if term.lower() not in original_title_words:
                    potential_new_title = f"{current_processed_title} {term}".strip()
                    if len(potential_new_title) <= max_title_chars:
                        current_processed_title = potential_new_title
                        original_title_words.add(term.lower())
            current_processed_title = " ".join(current_processed_title.split())

        # 4. Ensure title does not exceed maximum character length
        if len(current_processed_title) > max_title_chars:
            if png_suffix_was_added_in_this_step and png_isolated_phrase.lower() in current_processed_title.lower():
                expected_suffix_with_space = " " + png_isolated_phrase
                if current_processed_title.lower().endswith(expected_suffix_with_space.lower()):
                    base_part = current_processed_title[:-len(expected_suffix_with_space)]
                    allowable_len_for_base = max_title_chars - len(expected_suffix_with_space)
                    
                    if allowable_len_for_base > len(ellipsis) + 3:
                        truncated_base = smart_truncate_title(base_part.strip(), allowable_len_for_base, ellipsis)
                        current_processed_title = (truncated_base + expected_suffix_with_space).strip()
                    else: 
                        current_processed_title = smart_truncate_title(png_isolated_phrase, max_title_chars, ellipsis)
                else: 
                    current_processed_title = smart_truncate_title(current_processed_title, max_title_chars, ellipsis)
            else:
                current_processed_title = smart_truncate_title(current_processed_title, max_title_chars, ellipsis)
        
        if len(current_processed_title) > max_title_chars: # Final safety truncate
             current_processed_title = smart_truncate_title(current_processed_title, max_title_chars, ellipsis)

        # 5. Final clean and assign back to the main 'title' variable
        title = clean_metadata_text(current_processed_title, is_title=True)

        # Ensure title uniqueness within the session
        original_title_for_uniqueness_check = title
        title_unique_counter = 1
        while title.lower() in (t.lower() for t in generated_titles_in_session): # Case-insensitive check
            title = f"{original_title_for_uniqueness_check} {title_unique_counter}"
            title_unique_counter += 1
            # Re-check length and clean again if modified for uniqueness
            if len(title) > max_title_chars:
                 title = smart_truncate_title(title, max_title_chars, ellipsis)
            title = clean_metadata_text(title, is_title=True)
        generated_titles_in_session.add(title)
        # --- End of Refined Title Processing ---

        # Description is now directly from the consolidated API call (stored in 'description' variable)
        # Fallback logic if description is enabled but Gemini didn't provide one (or it was "Not requested" but still enabled)
        if description_enabled.get() and not description:
            # Create more specific fallback descriptions based on content
            first_keyword = keywords.split(',')[0].strip() if keywords else "content"

            if image_path.lower().endswith(('.svg', '.eps', '.ai')):
                description = f"Vector {first_keyword} featuring {title.lower()}. Scalable design suitable for various applications."
            else:
                description = f"{first_keyword.capitalize()} showing {title.lower()}. High resolution image suitable for commercial use."
            
            # Ensure fallback description respects length constraints
            min_chars_desc_fallback = min_description_var.get()
            max_chars_desc_fallback = max_description_var.get()
            description = clean_metadata_text(description) # Clean it first
            char_count_desc_fallback = len(description)

            if char_count_desc_fallback > max_chars_desc_fallback:
                if description[max_chars_desc_fallback-3:max_chars_desc_fallback] == "...": 
                    description = description[:max_chars_desc_fallback]
                else:
                    last_space = description.rfind(' ', 0, max_chars_desc_fallback - 3)
                    if last_space != -1 and max_chars_desc_fallback - last_space < 20 : 
                        description = description[:last_space] + "..."
                    else:
                        description = description[:max_chars_desc_fallback-3] + "..."
            elif char_count_desc_fallback < min_chars_desc_fallback:
                # Create more specific extensions based on file type and content
                file_ext = os.path.splitext(image_path)[1].lower()
                if file_ext in ['.svg', '.eps', '.ai']:
                    extension_fb = f" Scalable vector format suitable for print and digital media."
                elif file_ext in ['.png'] and has_transparency:
                    extension_fb = f" Transparent background allows easy integration into designs."
                else:
                    extension_fb = f" Clear details and vibrant colors enhance visual appeal."

                if char_count_desc_fallback + len(extension_fb) <= max_chars_desc_fallback:
                    description += extension_fb
                else:
                    remaining_space_fb = max_chars_desc_fallback - char_count_desc_fallback - 3
                    if remaining_space_fb > 0:
                        description += extension_fb[:remaining_space_fb] + "..."
                    elif char_count_desc_fallback > 0 :
                        description = description[:max_chars_desc_fallback-3] + "..." if char_count_desc_fallback > max_chars_desc_fallback -3 else description
        elif not description_enabled.get():
            description = "" # Ensure description is blank if not enabled

        # Clean black background phrases from final description if refine PNG BG is on
        if image_path.lower().endswith('.png') and png_refine_transparent_enabled.get() and description:
            black_background_phrases_to_remove = [
                "Black Background",
                "isolated on a Black Background",
                "Against Black Background",
                "Isolated Against a Black Background"
            ]
            for phrase_to_remove in black_background_phrases_to_remove:
                description = re.sub(r'(?i)\b' + re.escape(phrase_to_remove) + r'\b', '', description).strip()
            
            description = re.sub(r'\s\s+', ' ', description).strip()
            description = clean_metadata_text(description)

        if image_path.lower().endswith('.png') and png_isolated_enabled.get():
            if "isolated on a transparent background" not in description.lower() and "isolated on a white background" not in description.lower():
                description = f"{description} Isolated on a transparent background."
        item_id = None
        for item_tree in tree.get_children(): 
            values = tree.item(item_tree)["values"]
            if values and values[0] == os.path.basename(image_path):
                item_id = item_tree
                break
        if item_id:
            update_tree_item(item_id, (os.path.basename(image_path), title, keywords, description, shutterstock_category, "★★★★★"))
        else:
            root.after(0, lambda: tree.insert("", "end", values=(os.path.basename(image_path), title, keywords, description, shutterstock_category, "★★★★★")))
        print(f"📊 Updating statistics for single image: {os.path.basename(image_path)}")
        root.after(0, lambda: update_stats(os.path.basename(image_path)))
        root.after(0, lambda: update_stats_labels())
        root.after(0, lambda: status_message_label.config(text="File processed successfully", foreground="#008000"))

        # Auto embed metadata if enabled, otherwise show manual embed message
        if auto_embed_enabled.get():
            print(f"🔧 Scheduling auto embed for: {os.path.basename(image_path)}")
            root.after(100, lambda: auto_embed_metadata(image_path, title, keywords, description))
        else:
            print(f"📝 Metadata generated for: {os.path.basename(image_path)} - Use 'Embed Metadata' button to embed manually")
        return True
    except Exception as e:
        error_message = str(e)
        error_type = "Error"
        if "429" in error_message and retry_count < 3:  
            new_key = switch_to_next_api_key()
            if new_key:
                if item_id:
                    update_tree_item(item_id, (filename, "Retrying with different API key...", "", ""))
                root.after(0, lambda: update_api_key_status())
                return process_single_image(image_path, retry_count + 1)
            else:
                error_type = "Error: Rate Limit (429)"
                auto_embed_enabled.set(False)
                root.after(500, lambda: auto_embed_enabled.set(True))
        item_id = None
        for item_tree in tree.get_children(): 
            values = tree.item(item_tree)["values"]
            if values and values[0] == os.path.basename(image_path):
                item_id = item_tree
                break
        if item_id:
            update_tree_item(item_id, (os.path.basename(image_path), error_type, error_message, "", "Error", "☆☆☆☆☆"))
        else:
            root.after(0, lambda: tree.insert("", "end", values=(os.path.basename(image_path), error_type, error_message, "", "Error", "☆☆☆☆☆")))
        return False
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            try:
                os.remove(temp_image_path)
            except Exception as e:
                print(f"Error deleting temporary file: {e}")

def sanitize_filename(filename):
    sanitized = unicodedata.normalize('NFKD', filename).encode('ascii', 'ignore').decode('ascii')
    sanitized = re.sub(r'[^\w\-.]', '_', sanitized)
    return sanitized

def update_time_labels(start_time, total_start_time=None):
    current_time = time.time()
    elapsed_seconds = int(current_time - start_time)
    minutes, seconds = divmod(elapsed_seconds, 60)
    elapsed_label.config(text=f"Elapsed: {minutes:02d}:{seconds:02d}")
    if total_start_time:
        total_seconds = int(current_time - total_start_time)
        total_minutes, total_seconds_val = divmod(total_seconds, 60) 
        total_time_label.config(text=f"Total: {total_minutes:02d}:{total_seconds_val:02d}")
    if processing_active.get() and not stop_event.is_set():
        root.after(1000, lambda: update_time_labels(start_time, total_start_time))

def process_batch_images(batch_file_paths, retry_count=0):
    """Process a batch of up to 5 images in a single API call to reduce rate limits."""
    batch_size = len(batch_file_paths)
    print(f"🔄 Processing batch of {batch_size} images")

    # Load API key with better error handling
    try:
        api_key = load_gemini_api_key()
        print(f"📋 API key loaded: {'✅ Yes' if api_key else '❌ No'}")
    except Exception as e:
        print(f"❌ Error loading API key: {e}")
        return [False] * batch_size

    if not api_key:
        print("❌ No API key found")
        for file_path in batch_file_paths:
            filename = os.path.basename(file_path)
            for item in tree.get_children():
                values = tree.item(item)["values"]
                if values and values[0] == filename:
                    root.after(0, lambda i=item, f=filename: tree.item(i, values=(f, "Error: No API key", "", "", "", "☆☆☆☆☆")))
                    break
        root.after(0, lambda: messagebox.showerror("Gemini Error", "No API keys found. Please add at least one API key."))
        return [False] * batch_size

    # Configure Gemini API
    try:
        print(f"🔧 Configuring Gemini API...")
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel("gemini-2.5-flash-lite")
        print(f"✅ Gemini API configured successfully")
    except Exception as e:
        error_message = str(e)
        print(f"❌ Gemini API configuration failed: {error_message}")

        if "429" in error_message and retry_count < 3:
            print(f"🔄 Rate limit detected, trying to switch API key (attempt {retry_count + 1}/3)")
            new_key = switch_to_next_api_key()
            if new_key:
                root.after(0, lambda: update_api_key_status())
                return process_batch_images(batch_file_paths, retry_count + 1)

        # Update all items in batch with error
        for file_path in batch_file_paths:
            filename = os.path.basename(file_path)
            for item in tree.get_children():
                values = tree.item(item)["values"]
                if values and values[0] == filename:
                    root.after(0, lambda i=item, f=filename, e=error_message: tree.item(i, values=(f, "Error: API Setup Failed", e, "", "", "☆☆☆☆☆")))
                    break
        root.after(0, lambda e=error_message: messagebox.showerror("Gemini Setup Failed", f"API Error: {e}"))
        return [False] * batch_size

    # Prepare images for batch processing
    processed_images = []
    image_data = []
    valid_indices = []

    for idx, image_path in enumerate(batch_file_paths):
        filename = os.path.basename(image_path)
        print(f"🖼️ Preparing image {idx + 1}/{batch_size}: {filename}")

        # Update tree item to show processing
        for item in tree.get_children():
            values = tree.item(item)["values"]
            if values and values[0] == filename:
                root.after(0, lambda i=item, f=filename: tree.item(i, values=(f, "Processing...", "Processing...", "Processing...", "Processing...", "★★★★★")))
                break

        try:
            # Process image similar to process_single_image but collect data for batch
            temp_image_path = None

            # Handle different file types
            if image_path.lower().endswith(('.mp4', '.mov', '.avi', '.mkv')):
                # For videos, extract frame first
                with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp_img:
                    temp_image_path = tmp_img.name
                frame_path = extract_frame_from_video(image_path, temp_image_path, time_seconds=3)
                if not frame_path:
                    raise ValueError("Failed to extract frame from video")
                temp_image_path = frame_path
            elif image_path.lower().endswith('.svg'):
                with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_png:
                    temp_image_path = tmp_png.name
                cairosvg.svg2png(url=image_path, write_to=temp_image_path)
            elif image_path.lower().endswith('.ai'):
                with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_png:
                    temp_image_path = tmp_png.name
                ai_conversion_success = convert_ai_to_png_with_illustrator(image_path, temp_image_path)
                if not ai_conversion_success:
                    raise ValueError("AI to PNG conversion failed")
            elif image_path.lower().endswith('.eps'):
                with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_png:
                    temp_image_path = tmp_png.name
                convert_eps_to_png(image_path, temp_image_path)
            else:
                with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp_img:
                    temp_image_path = tmp_img.name
                with Image.open(image_path) as img:
                    if img.mode == "RGBA":
                        img = img.convert("RGB")
                    img.thumbnail((800, 800))
                    img.save(temp_image_path, format="JPEG")

            if temp_image_path and os.path.exists(temp_image_path):
                with Image.open(temp_image_path) as temp_img:
                    image_data.append({
                        'image': temp_img.copy(),
                        'path': image_path,
                        'temp_path': temp_image_path,
                        'filename': filename
                    })
                    valid_indices.append(idx)
                    processed_images.append(True)
            else:
                processed_images.append(False)

        except Exception as e:
            print(f"❌ Error preparing {filename}: {e}")
            processed_images.append(False)
            # Update tree item with error
            for item in tree.get_children():
                values = tree.item(item)["values"]
                if values and values[0] == filename:
                    root.after(0, lambda i=item, f=filename, err=str(e): tree.item(i, values=(f, "Error: Image Processing", err, "", "", "☆☆☆☆☆")))
                    break

    if not image_data:
        print("❌ No valid images to process in batch")
        return processed_images

    # Create batch prompt for all images
    print(f"🚀 Making batch API call to Gemini for {len(image_data)} images")

    try:
        # Build the batch prompt
        prompt_parts = [
            "You are an expert at analyzing images and creating accurate, content-focused metadata for microstock platforms. Your goal is to describe exactly what you see in each image.",
            f"I will provide you with {len(image_data)} images. For each image, analyze it and create metadata that accurately reflects its visual content.",
            "For each image, provide the following format:",
            "IMAGE_[NUMBER]:",
            "Title: [Generated Title]",
            "Keywords: [comma-separated keywords]",
            "Description: [Generated Description]",
            "Category: [Generated Shutterstock Category]",
            "",
            "Requirements for each image:",
            f"- Title: Create a specific, descriptive title that captures exactly what is shown. Target length: {min_title_words_var.get()}-{max_title_words_var.get()} characters. No punctuation.",
            f"- Keywords: Generate {min_keywords_var.get()}-{max_keywords_var.get()} specific and relevant keywords based on what you see.",
            f"- Description: Write a {min_description_var.get()}-{max_description_var.get()} character description focusing on visual elements." if description_enabled.get() else "- Description: Not requested",
            "- Category: Select from: Animals/Wildlife, Backgrounds/Textures, Beauty/Fashion, Buildings/Landmarks, Business/Finance, Celebrities, Education, Food and Drink, Healthcare/Medical, Holidays, Industrial, Interiors, Miscellaneous, Nature, Objects, People, Religion, Science, Signs/Symbols, Sports/Recreation, Technology, Transportation, Vintage/Retro",
            "",
            "CRITICAL INSTRUCTIONS:",
            "- Focus on describing actual visual content. Avoid generic terms like 'professional', 'high-quality', 'modern', 'creative' unless they specifically describe the visual style.",
            "- NEVER use spaced letters in words (e.g., write 'PNG' not 'P N G', '3D' not '3 D', 'transparent' not 'T R A N S P A R E N T')",
            "- For PNG images with transparency: ABSOLUTELY DO NOT use phrases like 'black background', 'on a black background', 'against black background', 'isolated on a black background'. Use 'white background', 'transparent background', or 'isolated' instead.",
            "- For transparent images: Describe as 'on a white background' or 'with transparent background', NEVER 'on a black background'.",
        ]

        # Add PNG-specific instructions if any PNG files are in the batch
        has_png_files = any(img_data['path'].lower().endswith('.png') for img_data in image_data)
        if has_png_files and png_refine_transparent_enabled.get():
            prompt_parts.insert(-1, "SPECIAL PNG INSTRUCTION: For any PNG images, NEVER generate metadata containing 'Black Background', 'isolated on a Black Background', 'Against Black Background', or 'Isolated Against a Black Background'. Focus on transparent or white backgrounds if applicable.")

        # Add custom prompt parts if configured
        custom_prompt_parts = config.get("custom_prompt_parts", "").strip()
        if custom_prompt_parts:
            prompt_parts.append(f"Additional instructions for all images: {custom_prompt_parts}")

        # Add each image to the prompt
        for i, img_data in enumerate(image_data, 1):
            prompt_parts.append(f"IMAGE_{i}:")
            prompt_parts.append(img_data['image'])

        # Make the API call
        response = model.generate_content(
            prompt_parts,
            generation_config={
                "temperature": 0.3,
                "max_output_tokens": (max_description_var.get() + 100) * len(image_data),
                "top_p": 0.95,
            }
        )

        print(f"✅ Batch API call successful")
        print(f"📄 Response length: {len(response.text) if response.text else 0} characters")

        # Parse the batch response
        response_text = response.text

        # Split response by IMAGE_ markers
        import re
        image_sections = re.split(r'IMAGE_\d+:', response_text)
        image_sections = [section.strip() for section in image_sections if section.strip()]

        # Process each image's metadata
        for i, img_data in enumerate(image_data):
            filename = img_data['filename']
            image_path = img_data['path']

            try:
                # Get the corresponding response section
                if i < len(image_sections):
                    metadata_text = image_sections[i]
                    metadata_lines = metadata_text.split('\n')

                    title = ""
                    keywords = ""
                    description = ""
                    shutterstock_category = "Miscellaneous"

                    # Parse metadata lines
                    for line in metadata_lines:
                        line = line.strip()
                        if line.startswith("Title: "):
                            raw_title_from_gemini = line.replace("Title: ", "").strip()
                            spaced_title = re.sub(r'(?<!^)(?=[A-Z])', ' ', raw_title_from_gemini)
                            title = clean_metadata_text(spaced_title, is_title=True)
                        elif line.startswith("Keywords: "):
                            keywords = clean_metadata_text(line.replace("Keywords: ", "").strip())
                        elif line.startswith("Description: "):
                            desc_text = line.replace("Description: ", "").strip()
                            if desc_text.lower() != "not requested":
                                description = clean_metadata_text(desc_text)
                        elif line.startswith("Category: "):
                            shutterstock_category = line.replace("Category: ", "").strip()

                    # CRITICAL FALLBACK LOGIC: Ensure all metadata components are generated
                    # This matches the single image processing fallback logic

                    # Track what was missing for debugging
                    missing_components = []
                    if not title:
                        missing_components.append("title")
                    if not keywords:
                        missing_components.append("keywords")
                    if description_enabled.get() and not description:
                        missing_components.append("description")

                    if missing_components:
                        print(f"⚠️ Missing components for {filename}: {', '.join(missing_components)} - applying fallbacks")

                    # Fallback for missing title
                    if not title and metadata_lines:
                        potential_title_line = metadata_lines[0]
                        if not potential_title_line.lower().startswith(("keywords:", "description:", "category:")):
                            title = clean_metadata_text(potential_title_line.strip(), is_title=True)

                    # Generate fallback title if still empty
                    if not title:
                        base_filename = os.path.splitext(filename)[0]
                        # Clean filename for title
                        title = re.sub(r'[_-]', ' ', base_filename)
                        title = re.sub(r'\d+', '', title).strip()
                        title = ' '.join(word.capitalize() for word in title.split() if len(word) > 2)
                        if not title:
                            title = "Stock Image"

                    # Generate fallback keywords if empty
                    if not keywords:
                        # Extract keywords from title and filename
                        title_words = [word.lower() for word in title.split() if len(word) > 3]
                        filename_words = [word.lower() for word in re.sub(r'[_-]', ' ', os.path.splitext(filename)[0]).split() if len(word) > 3]

                        # Combine and deduplicate
                        all_words = list(set(title_words + filename_words))

                        # Add generic keywords based on file type
                        if image_path.lower().endswith(('.svg', '.eps', '.ai')):
                            all_words.extend(['vector', 'illustration', 'design'])
                        elif image_path.lower().endswith('.png'):
                            all_words.extend(['image', 'graphic', 'design'])
                        else:
                            all_words.extend(['photo', 'image', 'stock'])

                        # Remove duplicates and limit to reasonable number
                        keywords = ', '.join(list(set(all_words))[:min_keywords_var.get()])
                        if not keywords:
                            keywords = "stock, image, design"

                    # Generate fallback description if enabled but empty
                    if description_enabled.get() and not description:
                        first_keyword = keywords.split(',')[0].strip() if keywords else "content"

                        if image_path.lower().endswith(('.svg', '.eps', '.ai')):
                            description = f"Vector {first_keyword} featuring {title.lower()}. Scalable design suitable for various applications."
                        else:
                            description = f"{first_keyword.capitalize()} showing {title.lower()}. High resolution image suitable for commercial use."

                    # Ensure title meets minimum character length (matching single image processing)
                    min_title_chars = min_title_words_var.get()
                    max_title_chars = max_title_words_var.get()

                    if len(title) < min_title_chars:
                        # Extract descriptive terms from keywords if available
                        descriptive_terms = []
                        if keywords:
                            keyword_list = [kw.strip() for kw in keywords.split(',')[:5]]
                            for kw in keyword_list:
                                if (len(kw) > 3 and
                                    kw.lower() not in ['professional', 'high', 'quality', 'modern', 'creative', 'design', 'premium', 'elegant'] and
                                    kw.lower() not in title.lower()):
                                    descriptive_terms.append(kw)

                        # If no good keywords, use file-type specific terms
                        if not descriptive_terms:
                            if image_path.lower().endswith(('.svg', '.eps', '.ai')):
                                descriptive_terms = ["vector", "illustration"]
                            elif image_path.lower().endswith('.png'):
                                descriptive_terms = ["graphic", "design"]
                            else:
                                descriptive_terms = ["image", "photo"]

                        # Add terms to title until minimum length is reached
                        original_title_words = {word.lower() for word in title.split()}
                        for term in descriptive_terms[:2]:
                            if len(title) >= min_title_chars:
                                break
                            if term.lower() not in original_title_words:
                                potential_new_title = f"{title} {term}".strip()
                                if len(potential_new_title) <= max_title_chars:
                                    title = potential_new_title
                                    original_title_words.add(term.lower())

                    # Ensure title doesn't exceed maximum length
                    if len(title) > max_title_chars:
                        # Truncate at word boundary
                        words = title.split()
                        truncated_title = ""
                        for word in words:
                            if len(truncated_title + " " + word) <= max_title_chars - 3:
                                truncated_title += (" " if truncated_title else "") + word
                            else:
                                break
                        title = truncated_title + "..." if truncated_title else title[:max_title_chars-3] + "..."

                    # Ensure description meets length requirements if enabled
                    if description_enabled.get() and description:
                        min_chars_desc = min_description_var.get()
                        max_chars_desc = max_description_var.get()
                        char_count_desc = len(description)

                        # Extend description if too short
                        if char_count_desc < min_chars_desc:
                            file_ext = os.path.splitext(image_path)[1].lower()
                            if file_ext in ['.svg', '.eps', '.ai']:
                                extension = " Scalable vector format suitable for print and digital media."
                            elif file_ext == '.png':
                                extension = " High quality image with transparent background support."
                            else:
                                extension = " Clear details and vibrant colors enhance visual appeal."

                            if char_count_desc + len(extension) <= max_chars_desc:
                                description += extension
                            else:
                                remaining_space = max_chars_desc - char_count_desc - 3
                                if remaining_space > 0:
                                    description += extension[:remaining_space] + "..."

                        # Truncate description if too long
                        elif char_count_desc > max_chars_desc:
                            if description[max_chars_desc-3:max_chars_desc] == "...":
                                description = description[:max_chars_desc]
                            else:
                                last_space = description.rfind(' ', 0, max_chars_desc - 3)
                                if last_space != -1 and max_chars_desc - last_space < 20:
                                    description = description[:last_space] + "..."
                                else:
                                    description = description[:max_chars_desc-3] + "..."
                    elif not description_enabled.get():
                        description = ""  # Ensure description is blank if not enabled

                    # Add custom title words and keywords
                    title = add_custom_title_words(title, max_title_words_var.get())
                    keywords = add_custom_keywords(keywords)

                    # Final cleaning
                    title = clean_metadata_text(title, is_title=True)
                    keywords = clean_metadata_text(keywords, is_title=False)
                    if description:
                        description = clean_metadata_text(description)

                    # Apply the same post-processing as in process_single_image
                    # First clean any unwanted letter spacing using a comprehensive function
                    def fix_letter_spacing(text):
                        if not text:
                            return text
                        # Fix spaced letters by repeatedly applying the pattern until no more matches
                        prev_text = ""
                        while prev_text != text:
                            prev_text = text
                            # Fix any sequence of single letters separated by spaces
                            text = re.sub(r'\b(\w)(\s+\w)+\b', lambda m: re.sub(r'\s+', '', m.group(0)), text)
                        return text

                    title = fix_letter_spacing(title)
                    keywords = fix_letter_spacing(keywords)
                    description = fix_letter_spacing(description)

                    # Clean black background phrases if refine PNG BG is on (match single image processing exactly)
                    if image_path.lower().endswith('.png') and png_refine_transparent_enabled.get():
                        # Order matters - longer phrases first to avoid partial matches
                        black_background_phrases_to_remove = [
                            "isolated on a Black Background",
                            "Isolated Against a Black Background",
                            "isolated on a black background",
                            "isolated against a black background",
                            "isolated against black background",
                            "against a black background",
                            "on a Black Background",
                            "Against Black Background",
                            "on a black background",
                            "against black background",
                            "on black background",
                            "with black background",
                            "black backdrop",
                            "on a black backdrop",
                            "against black backdrop",
                            "isolated on black background",
                            "Black Background",
                            "black background"
                        ]

                        # Clean title (using word boundaries for precise matching)
                        for phrase_to_remove in black_background_phrases_to_remove:
                            if title:
                                # Use word boundaries for precise matching
                                pattern = r'(?i)\b' + re.escape(phrase_to_remove) + r'\b'
                                title = re.sub(pattern, '', title)
                                title = re.sub(r'\s+', ' ', title).strip()  # Clean extra spaces after each removal

                        # Clean keywords (using word boundaries for precise matching)
                        for phrase_to_remove in black_background_phrases_to_remove:
                            if keywords:
                                pattern = r'(?i)\b' + re.escape(phrase_to_remove) + r'\b'
                                keywords = re.sub(pattern, '', keywords)
                                keywords = re.sub(r'\s*,\s*,\s*', ', ', keywords)  # Clean double commas
                                keywords = re.sub(r'^,\s*|,\s*$', '', keywords)  # Clean leading/trailing commas
                                keywords = re.sub(r'\s+', ' ', keywords).strip()  # Clean extra spaces

                        # Clean description (using word boundaries for precise matching)
                        for phrase_to_remove in black_background_phrases_to_remove:
                            if description:
                                pattern = r'(?i)\b' + re.escape(phrase_to_remove) + r'\b'
                                description = re.sub(pattern, '', description)
                                description = re.sub(r'\s+', ' ', description).strip()  # Clean extra spaces after each removal

                        # Final cleanup for description (match single image processing)
                        if description:
                            description = re.sub(r'\s\s+', ' ', description).strip()
                            description = clean_metadata_text(description)

                    # Apply PNG suffix if needed (after cleaning black background phrases)
                    if image_path.lower().endswith('.png') and png_isolated_enabled.get():
                        png_suffix_phrase = "isolated on a transparent background"
                        if not title.lower().endswith(png_suffix_phrase.lower()):
                            title = f"{title} {png_suffix_phrase}"

                    # Apply negative keywords filter
                    loaded_negative_keywords = [kw.strip().lower() for kw in negative_words_var.get().strip().split(',') if kw.strip()]
                    if loaded_negative_keywords:
                        # Filter Title
                        if title:
                            title_words = title.split()
                            title = " ".join([word for word in title_words if word.lower() not in loaded_negative_keywords])
                            title = clean_metadata_text(title, is_title=True)

                        # Filter Keywords
                        if keywords:
                            keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]
                            filtered_keyword_list = [kw for kw in keyword_list if kw.lower() not in loaded_negative_keywords]
                            keywords = ", ".join(filtered_keyword_list)
                            keywords = clean_metadata_text(keywords, is_title=False)

                        # Filter Description
                        if description:
                            temp_description = description
                            for neg_kw in loaded_negative_keywords:
                                temp_description = re.sub(r'\b' + re.escape(neg_kw) + r'\b', '', temp_description, flags=re.IGNORECASE)
                            description = clean_metadata_text(temp_description.strip())

                    # Update tree item with results
                    for item in tree.get_children():
                        values = tree.item(item)["values"]
                        if values and values[0] == filename:
                            root.after(0, lambda i=item, f=filename, t=title, k=keywords, d=description, c=shutterstock_category:
                                     tree.item(i, values=(f, t, k, d, c, "★★★★★")))
                            break

                    print(f"✅ Processed {filename}: {title[:50]}...")
                    processed_images[valid_indices[i]] = True

                    # Update statistics for successful processing (CRITICAL FIX)
                    print(f"📊 Updating statistics for batch item: {filename}")
                    root.after(0, lambda f=filename: update_stats(f))
                    root.after(0, lambda: update_stats_labels())

                    # Auto embed metadata if enabled, otherwise show manual embed message
                    if auto_embed_enabled.get():
                        print(f"🔧 Scheduling auto embed for batch item: {filename}")
                        root.after(200 + (i * 100), lambda path=image_path, t=title, k=keywords, d=description:
                                 auto_embed_metadata(path, t, k, d))
                    else:
                        print(f"📝 Metadata generated for batch item: {filename} - Use 'Embed Metadata' button to embed manually")

                else:
                    # No response section for this image
                    print(f"❌ No response section for {filename}")
                    for item in tree.get_children():
                        values = tree.item(item)["values"]
                        if values and values[0] == filename:
                            root.after(0, lambda i=item, f=filename: tree.item(i, values=(f, "Error: No response", "", "", "", "☆☆☆☆☆")))
                            break
                    processed_images[valid_indices[i]] = False

            except Exception as e:
                print(f"❌ Error processing response for {filename}: {e}")
                for item in tree.get_children():
                    values = tree.item(item)["values"]
                    if values and values[0] == filename:
                        root.after(0, lambda i=item, f=filename, err=str(e): tree.item(i, values=(f, "Error: Response parsing", err, "", "", "☆☆☆☆☆")))
                        break
                processed_images[valid_indices[i]] = False

        # Clean up temporary files
        for img_data in image_data:
            if img_data['temp_path'] and os.path.exists(img_data['temp_path']):
                try:
                    os.remove(img_data['temp_path'])
                except Exception:
                    pass

    except Exception as api_error:
        print(f"❌ Batch API call failed: {api_error}")
        error_message = str(api_error)

        # Check for rate limit and retry with different API key
        if "429" in error_message and retry_count < 3:
            print(f"🔄 Rate limit detected in batch, trying to switch API key (attempt {retry_count + 1}/3)")
            new_key = switch_to_next_api_key()
            if new_key:
                root.after(0, lambda: update_api_key_status())
                return process_batch_images(batch_file_paths, retry_count + 1)

        # Update all items in batch with error
        for file_path in batch_file_paths:
            filename = os.path.basename(file_path)
            for item in tree.get_children():
                values = tree.item(item)["values"]
                if values and values[0] == filename:
                    root.after(0, lambda i=item, f=filename, e=error_message: tree.item(i, values=(f, "Error: API call failed", e, "", "", "☆☆☆☆☆")))
                    break

        # Clean up temporary files
        for img_data in image_data:
            if img_data['temp_path'] and os.path.exists(img_data['temp_path']):
                try:
                    os.remove(img_data['temp_path'])
                except Exception:
                    pass

        return [False] * batch_size

    return processed_images

def process_images_worker(file_paths):
    total_files = len(file_paths)
    processed = 0
    start_time = time.time()
    total_start_time = start_time

    print(f"🎯 Worker started with {total_files} files (batch processing enabled)")

    def update_ui():
        if not stop_event.is_set():
            root.after(0, lambda: progress_bar.config(value=(processed / total_files) * 100 if total_files > 0 else 0))
            root.after(0, lambda: processed_label.config(text=f"Processed: {processed}/{total_files} ({int((processed/total_files)*100 if total_files > 0 else 0)}%)"))
            root.after(0, lambda: remaining_label.config(text=f"Remaining: {total_files - processed}"))
            current_time = time.time()
            elapsed_seconds = int(current_time - start_time)
            total_seconds = int(current_time - total_start_time)
            minutes, seconds = divmod(elapsed_seconds, 60)
            total_minutes, total_seconds_val = divmod(total_seconds, 60)
            root.after(0, lambda: elapsed_label.config(text=f"Elapsed: {minutes:02d}:{seconds:02d}"))
            root.after(0, lambda: total_time_label.config(text=f"Total: {total_minutes:02d}:{total_seconds_val:02d}"))

    # Check if batch processing is enabled (default: True for better rate limit handling)
    batch_processing_enabled = config.get('batch_processing_enabled', True)

    if batch_processing_enabled:
        # Process files in batches of 5
        batch_size = 5
        for batch_start in range(0, total_files, batch_size):
            if stop_event.is_set():
                print("🛑 Stop event detected, breaking batch loop")
                break

            print("⏸️ Waiting for pause event...")
            pause_event.wait()
            print("▶️ Pause event cleared, continuing...")

            batch_end = min(batch_start + batch_size, total_files)
            batch_files = file_paths[batch_start:batch_end]
            current_batch_size = len(batch_files)

            print(f"📦 Processing batch {batch_start//batch_size + 1}: files {batch_start + 1}-{batch_end} ({current_batch_size} files)")

            try:
                print("🔍 Checking rate limit...")
                can_proceed, wait_time = check_rate_limit()
                if not can_proceed:
                    formatted_wait = round(wait_time, 1)
                    print(f"⏳ Rate limit: waiting {formatted_wait} seconds")
                    if wait_time > 1.0:
                        message = f"Rate limit management: Waiting {formatted_wait} seconds... ({processed}/{total_files} files processed)"
                        color = "#FFA500"
                    else:
                        message = f"Processing batch... {processed}/{total_files} files completed (short delay: {formatted_wait}s)"
                        color = "#008000"
                    root.after(0, lambda msg=message, clr=color: status_message_label.config(text=msg, foreground=clr))
                    time.sleep(wait_time)

                # Process the batch
                batch_results = process_batch_images(batch_files)

                # Count successful processes
                batch_success_count = sum(1 for result in batch_results if result)
                processed += batch_success_count

                print(f"✅ Batch completed: {batch_success_count}/{current_batch_size} files successful")

                message = f"Processing... {processed}/{total_files} files completed"
                root.after(0, lambda: status_message_label.config(text=message, foreground="#008000"))
                update_ui()

            except Exception as e:
                error_message = str(e)
                if "429" in error_message:
                    wait_time = 2.0
                    import re
                    retry_match = re.search(r'retry after (\d+)', error_message.lower())
                    if retry_match:
                        retry_seconds = int(retry_match.group(1))
                        wait_time = min(retry_seconds, 5)
                    message = f"Rate limit hit. Smart retry in {wait_time} seconds... ({processed}/{total_files})"
                    root.after(0, lambda: status_message_label.config(text=message, foreground="#FFA500"))
                    time.sleep(wait_time)
                    continue
                print(f"Error processing batch: {e}")
    else:
        # Fallback to single image processing
        print("📝 Batch processing disabled, using single image processing")
        for file_index, file_path in enumerate(file_paths):
            print(f"📁 Processing file {file_index + 1}/{total_files}: {os.path.basename(file_path)}")

            if stop_event.is_set():
                print("🛑 Stop event detected, breaking loop")
                break

            print("⏸️ Waiting for pause event...")
            pause_event.wait()
            print("▶️ Pause event cleared, continuing...")

            try:
                print("🔍 Checking rate limit...")
                can_proceed, wait_time = check_rate_limit()
                if not can_proceed:
                    formatted_wait = round(wait_time, 1)
                    print(f"⏳ Rate limit: waiting {formatted_wait} seconds")
                    if wait_time > 1.0:
                        message = f"Rate limit management: Waiting {formatted_wait} seconds... ({processed}/{total_files} files processed)"
                        color = "#FFA500"
                    else:
                        message = f"Processing... {processed}/{total_files} files completed (short delay: {formatted_wait}s)"
                        color = "#008000"
                    root.after(0, lambda msg=message, clr=color: status_message_label.config(text=msg, foreground=clr))
                    time.sleep(wait_time)

                print(f"🔄 Starting to process: {os.path.basename(file_path)}")
                success = process_single_image(file_path)
                print(f"{'✅' if success else '❌'} Processing result for {os.path.basename(file_path)}: {success}")

                if success:
                    processed += 1
                message = f"Processing... {processed}/{total_files} files completed"
                root.after(0, lambda: status_message_label.config(text=message, foreground="#008000"))
                update_ui()
            except Exception as e:
                error_message = str(e)
                if "429" in error_message:
                    wait_time = 2.0
                    import re
                    retry_match = re.search(r'retry after (\d+)', error_message.lower())
                    if retry_match:
                        retry_seconds = int(retry_match.group(1))
                        wait_time = min(retry_seconds, 5)
                    message = f"Rate limit hit. Smart retry in {wait_time} seconds... ({processed}/{total_files})"
                    root.after(0, lambda: status_message_label.config(text=message, foreground="#FFA500"))
                    time.sleep(wait_time)
                    continue
                print(f"Error processing {file_path}: {e}")

    root.after(0, lambda: processing_active.set(False))
    root.after(0, lambda: elapsed_label.config(text="Elapsed: 00:00"))
    root.after(0, lambda: total_time_label.config(text="Total: 00:00"))
    root.after(0, lambda: status_message_label.config(
        text=f"Completed! {processed}/{total_files} files processed",
        foreground="#008000"
    ))

def process_images(file_paths):
    if not file_paths:
        messagebox.showinfo("No Files", "No files selected for processing.")
        return
    processing_active.set(True)
    stop_event.clear()
    processing_thread = threading.Thread(target=process_images_worker, args=(file_paths,))
    processing_thread.daemon = True  
    processing_thread.start()
    start_time = time.time()
    update_time_labels(start_time, start_time)

def select_images():
    file_paths = filedialog.askopenfilenames(filetypes=[("Image files", "*.jpg;*.jpeg;*.png")])
    if file_paths:
        image_listbox.delete(0, tk.END)
        for item in tree.get_children():
            tree.delete(item)
        for path in file_paths:
            filename = os.path.basename(path)
            image_listbox.insert(tk.END, path)
            tree.insert("", "end", values=(filename, "Pending...", "Pending...", "Pending...", "Pending...", "★★★★★"))
        selected_label.config(text=f"Selected: {len(file_paths)}")
        status_message_label.config(text="Files selected. Click 'Generate' to process.", foreground="#000000")

        # Check for PNG files and enable/disable PNG options
        check_png_files_selected()

def select_vectors():
    file_paths = filedialog.askopenfilenames(filetypes=[("Vector files", "*.svg;*.eps;*.ai")])
    if file_paths:
        image_listbox.delete(0, tk.END)
        for item in tree.get_children():
            tree.delete(item)
        for path in file_paths:
            filename = os.path.basename(path)
            image_listbox.insert(tk.END, path)
            tree.insert("", "end", values=(filename, "Pending...", "Pending...", "Pending...", "Pending...", "★★★★★"))
        selected_label.config(text=f"Selected: {len(file_paths)}")
        status_message_label.config(text="Files selected. Click 'Generate' to process.", foreground="#000000")

        # Check for PNG files and enable/disable PNG options (vectors won't have PNG, so will disable)
        check_png_files_selected()

def select_videos():
    file_paths = filedialog.askopenfilenames(
        filetypes=[("Video files", "*.mp4;*.mov;*.avi;*.mkv")]
    )
    if file_paths:
        image_listbox.delete(0, tk.END)
        for item in tree.get_children():
            tree.delete(item)
        for path in file_paths:
            filename = os.path.basename(path)
            image_listbox.insert(tk.END, path)
            tree.insert("", "end", values=(filename, "Pending...", "Pending...", "Pending...", "Pending...", "★★★★★"))
        selected_label.config(text=f"Selected: {len(file_paths)}")
        status_message_label.config(text="Files selected. Click 'Generate' to process.", foreground="#000000")

        # Check for PNG files and enable/disable PNG options (videos won't have PNG, so will disable)
        check_png_files_selected()

def select_folder():
    folder_path = filedialog.askdirectory()
    if folder_path:
        image_listbox.delete(0, tk.END)
        for item in tree.get_children():
            tree.delete(item)
        if mode_var.get() == "Image":
            valid_extensions = ('.jpg', '.jpeg', '.png')
        elif mode_var.get() == "Vector":
            valid_extensions = ('.svg', '.eps', '.ai')
        elif mode_var.get() == "Video":
            valid_extensions = ('.mp4', '.mov', '.avi', '.mkv')
        else:
            valid_extensions = ()
        for root_dir, _, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith(valid_extensions):
                    full_path = os.path.join(root_dir, file)
                    image_listbox.insert(tk.END, full_path)
                    tree.insert("", "end", values=(file, "Pending...", "Pending...", "Pending...", "Pending...", "★★★★★"))
        selected_label.config(text=f"Selected: {image_listbox.size()}")
        status_message_label.config(text="Files selected. Click 'Generate' to process.", foreground="#000000")

        # Check for PNG files and enable/disable PNG options
        check_png_files_selected()

def toggle_mode():
    """Update UI based on selected mode."""
    mode = mode_var.get()

    if mode == "Prompt Generator":
        # Hide main metadata generator UI
        main_frame.pack_forget()
        # Hide status elements that should only show in metadata generator mode
        if 'progress_bar' in globals():
            progress_bar.pack_forget()
        if 'status_frame' in globals():
            status_frame.pack_forget()
        if 'info_frame' in globals():
            info_frame.pack_forget()
        # Show prompt generator UI
        prompt_generator_frame.pack(fill="both", expand=True)
    else:
        # Hide prompt generator UI
        if 'prompt_generator_frame' in globals():
            prompt_generator_frame.pack_forget()
        # Show main metadata generator UI
        main_frame.pack(fill="both", expand=True)
        # Show status elements for metadata generator mode
        if 'progress_bar' in globals():
            progress_bar.pack(fill=tk.X, pady=(5, 0))
        if 'status_frame' in globals():
            status_frame.pack(fill=tk.X, pady=(5, 2))
        if 'info_frame' in globals():
            info_frame.pack(fill=tk.X, pady=(0, 0))

        # Default states for metadata generator modes
        if 'auto_embed_btn' in globals() and auto_embed_btn:
            auto_embed_btn.config(state=tk.NORMAL)
        if 'illustrator_embed_button' in globals() and illustrator_embed_button:
            illustrator_embed_button.config(state=tk.NORMAL if mode == "Vector" else tk.DISABLED)
        if 'manual_embed_button' in globals() and manual_embed_button:
            manual_embed_button.config(state=tk.NORMAL)  # Always enabled for manual embedding

        if 'video_options_frame' in globals() and video_options_frame:
            video_options_frame.pack_forget()

        if mode == "Image":
            select_button.config(text="SELECT IMAGES", command=select_images)
            if 'auto_embed_btn' in globals() and auto_embed_btn:
                auto_embed_btn.config(state=tk.NORMAL)
            if 'illustrator_embed_button' in globals() and illustrator_embed_button:
                 illustrator_embed_button.config(state=tk.DISABLED)
            if 'manual_embed_button' in globals() and manual_embed_button:
                 manual_embed_button.config(state=tk.NORMAL)
        elif mode == "Vector":
            select_button.config(text="SELECT VECTORS", command=select_vectors)
            if 'auto_embed_btn' in globals() and auto_embed_btn:
                auto_embed_btn.config(state=tk.NORMAL)  # Enable auto-embed for vector files
            if 'illustrator_embed_button' in globals() and illustrator_embed_button:
                illustrator_embed_button.config(state=tk.NORMAL)
            if 'manual_embed_button' in globals() and manual_embed_button:
                 manual_embed_button.config(state=tk.NORMAL)  # Enable for vector files too
        elif mode == "Video":
            select_button.config(text="SELECT VIDEOS", command=select_videos)
            if 'auto_embed_btn' in globals() and auto_embed_btn:
                auto_embed_btn.config(state=tk.DISABLED)  # Disable auto-embed for videos (manual only)
            if 'illustrator_embed_button' in globals() and illustrator_embed_button:
                illustrator_embed_button.config(state=tk.DISABLED)
            if 'manual_embed_button' in globals() and manual_embed_button:
                 manual_embed_button.config(state=tk.NORMAL)  # Enable for video files
            if 'video_options_frame' in globals() and video_options_frame:
                video_options_frame.pack(side=tk.LEFT, padx=(0,5), fill=tk.Y, anchor="n")
        clear_data()

def clear_data():
    if processing_active.get():
        if not messagebox.askyesno("Confirm Clear", "Processing is active. Are you sure you want to clear all data and stop processing?"):
            return
    stop_event.set()
    processing_active.set(False)
    image_listbox.delete(0, tk.END)
    for item in tree.get_children():
        tree.delete(item)
    progress_bar["value"] = 0
    selected_label.config(text="Selected: 0")
    processed_label.config(text="Processed: 0/0 (0%)")
    remaining_label.config(text="Remaining: 0")
    elapsed_label.config(text="Elapsed: 00:00")
    total_time_label.config(text="Total: 00:00")
    status_message_label.config(text="Ready", foreground="#000000")
    pause_event.set()
    pause_button.config(text="Pause", bootstyle=WARNING)
    generated_titles_in_session.clear() # Clear session titles

    # Check for PNG files and enable/disable PNG options (will disable since no files)
    check_png_files_selected()

# Define the directory and files for storing API keys
APPDATA_DIR = os.path.join(os.getenv("APPDATA"), "MetaMaster")
API_KEY_FILE = os.path.join(APPDATA_DIR, "api_key.txt")  # Keep for backward compatibility
API_KEYS_FILE = os.path.join(APPDATA_DIR, "api_keys.json")  # New file for multiple keys
CURRENT_KEY_INDEX_FILE = os.path.join(APPDATA_DIR, "current_key_index.txt")  # Track current key index

# Ensure the directory exists
os.makedirs(APPDATA_DIR, exist_ok=True)

# Global variable to track the current API key index
current_api_key_index = 0

def validate_gemini_api_key(api_key, show_messages=True, update_button=True):
    """Validate the Gemini API key by making a test request."""
    try:
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-flash-lite')  # Updated to use Flash model
        # Make a simple test request
        response = model.generate_content("Test.")

        # Only update button if requested (for the main UI)
        if update_button and 'validate_key_button' in globals():
            globals()['validate_key_button'].configure(bootstyle="success")  # Change button to green

        if show_messages:
            messagebox.showinfo("Success", "API key is valid!")
        return True, "API key is valid!"
    except Exception as e:
        error_message = str(e)

        # Only update button if requested (for the main UI)
        if update_button and 'validate_key_button' in globals():
            globals()['validate_key_button'].configure(bootstyle="danger")  # Change button to red

        if "403" in error_message:
            if show_messages:
                messagebox.showerror("Error", "Invalid API key. Please check and try again.")
            return False, "Invalid API key. Please check and try again."
        elif "429" in error_message:
            if show_messages:
                messagebox.showerror("Error", "Rate limit exceeded. Please try again later.")
            return False, "Rate limit exceeded. Please try again later."
        else:
            if show_messages:
                messagebox.showerror("Error", f"Error validating API key: {error_message}")
            return False, f"Error validating API key: {error_message}"

def manage_api_keys():
    """Open a window to manage multiple API keys."""
    # Create a new window
    api_keys_window = ttk.Toplevel(root)
    api_keys_window.title("Manage API Keys")
    api_keys_window.geometry("700x500")
    api_keys_window.resizable(True, True)

    # Create main frame
    main_frame = ttk.Frame(api_keys_window, padding="10")
    main_frame.pack(fill="both", expand=True)

    # Create a frame for the API key list
    list_frame = ttk.LabelFrame(main_frame, text="API Keys", padding="10")
    list_frame.pack(fill="both", expand=True, pady=(0, 10))

    # Create a treeview to display the API keys
    columns = ("index", "label", "key", "status", "last_used")
    api_keys_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)

    # Define column headings
    api_keys_tree.heading("index", text="#")
    api_keys_tree.heading("label", text="Label")
    api_keys_tree.heading("key", text="API Key")
    api_keys_tree.heading("status", text="Status")
    api_keys_tree.heading("last_used", text="Last Used")

    # Define column widths
    api_keys_tree.column("index", width=30, anchor="center")
    api_keys_tree.column("label", width=150)
    api_keys_tree.column("key", width=250)
    api_keys_tree.column("status", width=100)
    api_keys_tree.column("last_used", width=150)

    # Add scrollbar
    scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=api_keys_tree.yview)
    api_keys_tree.configure(yscrollcommand=scrollbar.set)
    scrollbar.pack(side="right", fill="y")
    api_keys_tree.pack(fill="both", expand=True)

    # Create a frame for adding new API keys
    add_frame = ttk.LabelFrame(main_frame, text="Add New API Key", padding="10")
    add_frame.pack(fill="x", pady=(0, 10))

    # Label field
    label_frame = ttk.Frame(add_frame)
    label_frame.pack(fill="x", pady=(0, 5))

    label_label = ttk.Label(label_frame, text="Label:")
    label_label.pack(side="left", padx=(0, 5))

    label_entry = ttk.Entry(label_frame, width=30)
    label_entry.pack(side="left", fill="x", expand=True)

    # API Key field
    key_frame = ttk.Frame(add_frame)
    key_frame.pack(fill="x", pady=(0, 5))

    key_label = ttk.Label(key_frame, text="API Key:")
    key_label.pack(side="left", padx=(0, 5))

    key_entry = ttk.Entry(key_frame, width=50, show="*")
    key_entry.pack(side="left", fill="x", expand=True)

    # Show/hide key toggle
    show_key_var = tk.BooleanVar(value=False)

    def toggle_key_visibility():
        if show_key_var.get():
            key_entry.config(show="")
        else:
            key_entry.config(show="*")

    show_key_check = ttk.Checkbutton(
        key_frame,
        text="Show Key",
        variable=show_key_var,
        command=toggle_key_visibility,
        bootstyle="round-toggle"
    )
    show_key_check.pack(side="left", padx=(5, 0))

    # Buttons frame
    buttons_frame = ttk.Frame(add_frame)
    buttons_frame.pack(fill="x", pady=(5, 0))

    # Function to validate and add a new API key
    def add_api_key():
        label = label_entry.get().strip()
        key = key_entry.get().strip()

        if not label:
            messagebox.showerror("Error", "Please enter a label for the API key.")
            return

        if not key:
            messagebox.showerror("Error", "Please enter an API key.")
            return

        # Validate the API key
        is_valid, message = validate_gemini_api_key(key, show_messages=False, update_button=False)

        # Get current keys
        api_keys = load_api_keys()

        # Add the new key
        new_key = {
            "key": key,
            "label": label,
            "status": "Valid" if is_valid else "Invalid",
            "last_used": datetime.now().isoformat() if is_valid else None
        }

        api_keys.append(new_key)
        save_api_keys(api_keys)

        # Clear the entry fields
        label_entry.delete(0, tk.END)
        key_entry.delete(0, tk.END)

        # Refresh the list
        refresh_api_keys_list()

        # Show success message
        if is_valid:
            messagebox.showinfo("Success", f"API key '{label}' added successfully!")
        else:
            messagebox.showwarning("Warning", f"API key '{label}' added but validation failed: {message}")

    # Function to validate the selected API key
    def validate_selected_key():
        selected_items = api_keys_tree.selection()
        if not selected_items:
            messagebox.showerror("Error", "Please select an API key to validate.")
            return

        # Get the selected item
        item_id = selected_items[0]
        item_values = api_keys_tree.item(item_id, "values")
        index = int(item_values[0]) - 1

        # Get all keys
        api_keys = load_api_keys()

        if index < 0 or index >= len(api_keys):
            messagebox.showerror("Error", "Invalid selection.")
            return

        # Validate the key
        is_valid, message = validate_gemini_api_key(api_keys[index]["key"], show_messages=True, update_button=False)

        # Update the key's status
        api_keys[index]["status"] = "Valid" if is_valid else "Invalid"
        if is_valid:
            api_keys[index]["last_used"] = datetime.now().isoformat()

        save_api_keys(api_keys)

        # Refresh the list
        refresh_api_keys_list()

    # Function to remove the selected API key
    def remove_selected_key():
        selected_items = api_keys_tree.selection()
        if not selected_items:
            messagebox.showerror("Error", "Please select an API key to remove.")
            return

        # Get the selected item
        item_id = selected_items[0]
        item_values = api_keys_tree.item(item_id, "values")
        index = int(item_values[0]) - 1
        label = item_values[1]

        # Confirm deletion
        if not messagebox.askyesno("Confirm Deletion", f"Are you sure you want to remove the API key '{label}'?"):
            return

        # Get all keys
        api_keys = load_api_keys()

        if index < 0 or index >= len(api_keys):
            messagebox.showerror("Error", "Invalid selection.")
            return

        # Remove the key
        api_keys.pop(index)
        save_api_keys(api_keys)

        # Reset current key index if needed
        global current_api_key_index
        if current_api_key_index >= len(api_keys):
            current_api_key_index = 0 if api_keys else -1
            save_current_key_index(current_api_key_index)

        # Refresh the list
        refresh_api_keys_list()

        # Show success message
        messagebox.showinfo("Success", f"API key '{label}' removed successfully!")

    # Function to set the selected API key as active
    def set_as_active_key():
        selected_items = api_keys_tree.selection()
        if not selected_items:
            messagebox.showerror("Error", "Please select an API key to set as active.")
            return

        # Get the selected item
        item_id = selected_items[0]
        item_values = api_keys_tree.item(item_id, "values")
        index = int(item_values[0]) - 1
        label = item_values[1]

        # Get all keys
        api_keys = load_api_keys()

        if index < 0 or index >= len(api_keys):
            messagebox.showerror("Error", "Invalid selection.")
            return

        # Set as active
        global current_api_key_index
        current_api_key_index = index
        save_current_key_index(current_api_key_index)

        # Refresh the list
        refresh_api_keys_list()

        # Show success message
        messagebox.showinfo("Success", f"API key '{label}' set as active!")

    # Function to refresh the API keys list
    def refresh_api_keys_list():
        # Clear the treeview
        for item in api_keys_tree.get_children():
            api_keys_tree.delete(item)

        # Get all keys
        api_keys = load_api_keys()

        # Add keys to the treeview
        for i, key_data in enumerate(api_keys):
            # Mask the API key for display
            masked_key = key_data["key"][:8] + "..." + key_data["key"][-4:] if len(key_data["key"]) > 12 else key_data["key"]

            # Format the last used date
            last_used = key_data.get("last_used", "Never")
            if last_used and last_used != "Never":
                try:
                    dt = datetime.fromisoformat(last_used)
                    last_used = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    pass

            # Add active indicator
            label = key_data["label"]
            if i == current_api_key_index:
                label = "✓ " + label

            api_keys_tree.insert("", "end", values=(
                i + 1,
                label,
                masked_key,
                key_data.get("status", "Unknown"),
                last_used
            ))

    # Add buttons
    validate_button = ttk.Button(
        buttons_frame,
        text="Validate",
        command=lambda: validate_selected_key(),
        bootstyle=PRIMARY
    )
    validate_button.pack(side="left", padx=(0, 5))

    set_active_button = ttk.Button(
        buttons_frame,
        text="Set as Active",
        command=lambda: set_as_active_key(),
        bootstyle=SUCCESS
    )
    set_active_button.pack(side="left", padx=(0, 5))

    remove_button = ttk.Button(
        buttons_frame,
        text="Remove",
        command=lambda: remove_selected_key(),
        bootstyle=DANGER
    )
    remove_button.pack(side="left", padx=(0, 5))

    add_button = ttk.Button(
        buttons_frame,
        text="Add New Key",
        command=add_api_key,
        bootstyle=SUCCESS
    )
    add_button.pack(side="right")

    # Help text
    help_frame = ttk.LabelFrame(main_frame, text="Help", padding="10")
    help_frame.pack(fill="x")

    help_text = ttk.Label(
        help_frame,
        text="• Add multiple API keys to avoid rate limit errors\n"
             "• The application will automatically switch to the next key when a rate limit is encountered\n"
             "• The active key is marked with a checkmark (✓)\n"
             "• Get your API key from: https://aistudio.google.com/apikey",
        justify="left"
    )
    help_text.pack(fill="x")

    # Get API button
    get_api_button = ttk.Button(
        help_frame,
        text="Get API Key",
        command=open_get_api_link,
        bootstyle=INFO
    )
    get_api_button.pack(pady=(5, 0))

    # Initial refresh
    refresh_api_keys_list()

    # Make the window modal
    api_keys_window.transient(root)
    api_keys_window.grab_set()
    root.wait_window(api_keys_window)

def save_api_key(api_key, label="Main Key"):
    """Save an API key to both the old and new storage formats."""
    if not api_key:
        return False, "Empty API key"

    # Validate the API key
    is_valid, message = validate_gemini_api_key(api_key, show_messages=False, update_button=False)

    # Save to old format for backward compatibility
    with open(API_KEY_FILE, "w") as f:
        f.write(api_key)
    config["api_key"] = api_key
    save_config(config)

    # Also save to the new format
    api_keys = load_api_keys()
    # Check if this key already exists
    key_exists = False
    for key_data in api_keys:
        if key_data["key"] == api_key:
            key_exists = True
            # Update the existing key's status
            key_data["status"] = "Valid" if is_valid else "Invalid"
            key_data["last_used"] = datetime.now().isoformat() if is_valid else key_data.get("last_used", None)
            break

    if not key_exists:
        api_keys.append({
            "key": api_key,
            "label": label,
            "status": "Valid" if is_valid else "Invalid",
            "last_used": datetime.now().isoformat() if is_valid else None
        })

    save_api_keys(api_keys)

    # Update the status indicator
    update_api_key_status()

    return is_valid, message

def open_settings_window():
    """Open a comprehensive settings window for configuring all parameters."""
    # Create a new window
    settings_window = ttk.Toplevel(root)
    settings_window.title("Meta Master Settings")
    settings_window.geometry("700x650") # Increased height for new setting
    settings_window.resizable(True, True)

    # Create main frame with scrolling
    main_container = ttk.Frame(settings_window)
    main_container.pack(fill="both", expand=True)

    # Add a canvas for scrolling
    canvas = tk.Canvas(main_container)
    scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    # Add mouse wheel scrolling support (cross-platform)
    def on_mousewheel(event):
        """Handle mouse wheel scrolling for Windows and macOS."""
        canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def on_mousewheel_linux(event):
        """Handle mouse wheel scrolling for Linux."""
        if event.num == 4:
            canvas.yview_scroll(-1, "units")
        elif event.num == 5:
            canvas.yview_scroll(1, "units")

    def bind_mousewheel(event):
        """Bind mouse wheel events when mouse enters the canvas."""
        canvas.bind_all("<MouseWheel>", on_mousewheel)  # Windows and macOS
        canvas.bind_all("<Button-4>", on_mousewheel_linux)  # Linux scroll up
        canvas.bind_all("<Button-5>", on_mousewheel_linux)  # Linux scroll down

    def unbind_mousewheel(event):
        """Unbind mouse wheel events when mouse leaves the canvas."""
        canvas.unbind_all("<MouseWheel>")
        canvas.unbind_all("<Button-4>")
        canvas.unbind_all("<Button-5>")

    # Bind mouse wheel events to canvas and scrollable frame
    canvas.bind('<Enter>', bind_mousewheel)
    canvas.bind('<Leave>', unbind_mousewheel)
    scrollable_frame.bind('<Enter>', bind_mousewheel)
    scrollable_frame.bind('<Leave>', unbind_mousewheel)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Title section
    title_section = ttk.LabelFrame(scrollable_frame, text="Title Settings", padding="10")
    title_section.pack(fill="x", padx=10, pady=5)
    title_section.columnconfigure(1, weight=1) # Allow scale to expand

    # Min Title Characters
    min_title_label = ttk.Label(title_section, text="Minimum Title Characters:", width=25)
    min_title_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
    settings_min_title_var = tk.IntVar(value=min_title_words_var.get())
    min_title_scale = ttk.Scale(
        title_section,
        from_=80,
        to=100,
        variable=settings_min_title_var,
        length=300, # Length can be adjusted or removed if using grid expansion
        bootstyle="success",
        command=lambda val: settings_min_title_var.set(int(float(val)))
    )
    min_title_scale.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
    min_title_value_label = ttk.Label(title_section, textvariable=settings_min_title_var, width=4)
    min_title_value_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)

    # Max Title Characters
    max_title_label = ttk.Label(title_section, text="Maximum Title Characters:", width=25)
    max_title_label.grid(row=1, column=0, sticky="w", padx=5, pady=5)
    settings_max_title_var = tk.IntVar(value=max_title_words_var.get())
    max_title_scale = ttk.Scale(
        title_section,
        from_=100,
        to=200,
        variable=settings_max_title_var,
        length=300,
        bootstyle="danger",
        command=lambda val: settings_max_title_var.set(int(float(val)))
    )
    max_title_scale.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
    max_title_value_label = ttk.Label(title_section, textvariable=settings_max_title_var, width=4)
    max_title_value_label.grid(row=1, column=2, sticky="w", padx=5, pady=5)

    # Keywords section
    keywords_section = ttk.LabelFrame(scrollable_frame, text="Keywords Settings", padding="10")
    keywords_section.pack(fill="x", padx=10, pady=5)
    keywords_section.columnconfigure(1, weight=1)

    # Min Keywords
    min_keywords_label = ttk.Label(keywords_section, text="Minimum Keywords:", width=25)
    min_keywords_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
    settings_min_keywords_var = tk.IntVar(value=min_keywords_var.get())
    min_keywords_scale = ttk.Scale(
        keywords_section,
        from_=10,
        to=40,
        variable=settings_min_keywords_var,
        length=300,
        bootstyle="success",
        command=lambda val: settings_min_keywords_var.set(int(float(val)))
    )
    min_keywords_scale.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
    min_keywords_value_label = ttk.Label(keywords_section, textvariable=settings_min_keywords_var, width=4)
    min_keywords_value_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)

    # Max Keywords
    max_keywords_label = ttk.Label(keywords_section, text="Maximum Keywords:", width=25)
    max_keywords_label.grid(row=1, column=0, sticky="w", padx=5, pady=5)
    settings_max_keywords_var = tk.IntVar(value=max_keywords_var.get())
    max_keywords_scale = ttk.Scale(
        keywords_section,
        from_=20,
        to=50,
        variable=settings_max_keywords_var,
        length=300,
        bootstyle="danger",
        command=lambda val: settings_max_keywords_var.set(int(float(val)))
    )
    max_keywords_scale.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
    max_keywords_value_label = ttk.Label(keywords_section, textvariable=settings_max_keywords_var, width=4)
    max_keywords_value_label.grid(row=1, column=2, sticky="w", padx=5, pady=5)

    # Single-Word Keywords Toggle
    settings_single_word_keywords_var = tk.BooleanVar(value=single_word_keywords_var.get())
    single_word_keywords_check = ttk.Checkbutton(
        keywords_section,
        text="Generate Single-Word Keywords Only",
        variable=settings_single_word_keywords_var,
        bootstyle="round-toggle"
    )
    single_word_keywords_check.grid(row=2, column=0, columnspan=3, sticky="w", padx=5, pady=5)


    # Description section
    description_section = ttk.LabelFrame(scrollable_frame, text="Description Settings", padding="10")
    description_section.pack(fill="x", padx=10, pady=5)
    description_section.columnconfigure(1, weight=1)

    # Min Description Characters
    min_description_label = ttk.Label(description_section, text="Minimum Description Characters:", width=25)
    min_description_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
    settings_min_description_var = tk.IntVar(value=min_description_var.get())
    min_description_scale = ttk.Scale(
        description_section,
        from_=80,
        to=100,
        variable=settings_min_description_var,
        length=300,
        bootstyle="success",
        command=lambda val: settings_min_description_var.set(int(float(val)))
    )
    min_description_scale.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
    min_description_value_label = ttk.Label(description_section, textvariable=settings_min_description_var, width=4)
    min_description_value_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)

    # Max Description Characters
    max_description_label = ttk.Label(description_section, text="Maximum Description Characters:", width=25)
    max_description_label.grid(row=1, column=0, sticky="w", padx=5, pady=5)
    settings_max_description_var = tk.IntVar(value=max_description_var.get())
    max_description_scale = ttk.Scale(
        description_section,
        from_=100,
        to=200,
        variable=settings_max_description_var,
        length=300,
        bootstyle="danger",
        command=lambda val: settings_max_description_var.set(int(float(val)))
    )
    max_description_scale.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
    max_description_value_label = ttk.Label(description_section, textvariable=settings_max_description_var, width=4)
    max_description_value_label.grid(row=1, column=2, sticky="w", padx=5, pady=5)

    # Processing Settings section
    processing_section = ttk.LabelFrame(scrollable_frame, text="Processing Settings", padding="10")
    processing_section.pack(fill="x", padx=10, pady=5)

    # Batch Processing Toggle
    settings_batch_processing_var = tk.BooleanVar(value=config.get("batch_processing_enabled", True))
    batch_processing_check = ttk.Checkbutton(
        processing_section,
        text="Enable Batch Processing (Process 5 images per API call to reduce rate limits)",
        variable=settings_batch_processing_var
    )
    batch_processing_check.pack(anchor="w", padx=5, pady=5)

    # Add info label for batch processing
    batch_info_label = ttk.Label(
        processing_section,
        text="Batch processing sends 5 images at once to Gemini API, reducing rate limit errors.\nDisable if you experience issues with batch processing.",
        foreground="gray"
    )
    batch_info_label.pack(anchor="w", padx=5, pady=(0, 5))

    # Custom Prompt Parts section
    prompt_section = ttk.LabelFrame(scrollable_frame, text="Custom Prompt Parts", padding="10")
    prompt_section.pack(fill="x", padx=10, pady=5)

    # Custom prompt parts text area
    prompt_label = ttk.Label(prompt_section, text="Additional instructions for metadata generation:")
    prompt_label.pack(anchor="w", padx=5, pady=(0, 5))

    # Create frame for text area and scrollbar
    prompt_text_frame = ttk.Frame(prompt_section)
    prompt_text_frame.pack(fill="x", padx=5, pady=5)

    # Text area for custom prompt parts
    custom_prompt_text = tk.Text(
        prompt_text_frame,
        height=4,
        width=60,
        wrap=tk.WORD,
        font=("Segoe UI", 9)
    )
    custom_prompt_text.pack(side="left", fill="both", expand=True)

    # Scrollbar for text area
    prompt_scrollbar = ttk.Scrollbar(prompt_text_frame, orient="vertical", command=custom_prompt_text.yview)
    prompt_scrollbar.pack(side="right", fill="y")
    custom_prompt_text.config(yscrollcommand=prompt_scrollbar.set)

    # Load existing custom prompt parts
    existing_custom_prompt = config.get("custom_prompt_parts", "")
    custom_prompt_text.insert("1.0", existing_custom_prompt)

    # Info label for custom prompt parts
    prompt_info_label = ttk.Label(
        prompt_section,
        text="These instructions will be added to the AI prompt for metadata generation.\nExample: 'Focus on colors and emotions', 'Avoid technical terms', etc.",
        foreground="gray"
    )
    prompt_info_label.pack(anchor="w", padx=5, pady=(5, 0))

    # Vector Embedding Settings section
    vector_section = ttk.LabelFrame(scrollable_frame, text="Vector Embedding Settings", padding="10")
    vector_section.pack(fill="x", padx=10, pady=5)

    # Vector embed without export option
    settings_vector_embed_without_export_var = tk.BooleanVar(value=config.get("vector_embed_without_export", False))
    vector_embed_without_export_check = ttk.Checkbutton(
        vector_section,
        text="Embed vector metadata without exporting JPG images",
        variable=settings_vector_embed_without_export_var
    )
    vector_embed_without_export_check.pack(anchor="w", padx=5, pady=5)

    # Info label for vector embed without export
    vector_embed_info_label = ttk.Label(
        vector_section,
        text="When enabled, vector files will have metadata embedded directly without creating JPG exports.\nThis is faster but JPG previews won't be generated.",
        foreground="gray"
    )
    vector_embed_info_label.pack(anchor="w", padx=5, pady=(0, 10))

    # EPS Version Selection
    eps_version_label = ttk.Label(vector_section, text="EPS Compatibility Version:")
    eps_version_label.pack(anchor="w", padx=5, pady=(5, 0))

    # EPS version selection frame
    eps_version_frame = ttk.Frame(vector_section)
    eps_version_frame.pack(anchor="w", padx=5, pady=5)

    settings_eps_version_var = tk.StringVar(value=config.get("eps_version", "illustrator10"))

    eps_v10_radio = ttk.Radiobutton(
        eps_version_frame,
        text="Illustrator 10 EPS (Recommended for microstock)",
        variable=settings_eps_version_var,
        value="illustrator10"
    )
    eps_v10_radio.pack(anchor="w", pady=2)

    eps_v2020_radio = ttk.Radiobutton(
        eps_version_frame,
        text="Illustrator 2020 EPS (Latest compatibility)",
        variable=settings_eps_version_var,
        value="illustrator2020"
    )
    eps_v2020_radio.pack(anchor="w", pady=2)

    # Info label for EPS version
    eps_version_info_label = ttk.Label(
        vector_section,
        text="Illustrator 10 EPS: Better compatibility with microstock sites, smaller file sizes.\nIllustrator 2020 EPS: Latest features, may have larger file sizes.",
        foreground="gray"
    )
    eps_version_info_label.pack(anchor="w", padx=5, pady=(5, 0))

    # Custom Title Words section
    custom_title_words_section = ttk.LabelFrame(scrollable_frame, text="Custom Title Words", padding="10")
    custom_title_words_section.pack(fill="x", padx=10, pady=5)
    custom_title_words_section.columnconfigure(1, weight=1)

    custom_title_words_info = ttk.Label(
        custom_title_words_section,
        text="Custom words that will be automatically added to generated titles. Separate multiple words with commas.",
        wraplength=650
    )
    custom_title_words_info.grid(row=0, column=0, columnspan=3, sticky="w", padx=5, pady=(0, 5))

    # Title words text area
    ttk.Label(custom_title_words_section, text="Title Words:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
    custom_title_words_text = tk.Text(custom_title_words_section, height=2, width=50)
    custom_title_words_text.insert("1.0", config.get("custom_title_words", ""))
    custom_title_words_text.grid(row=1, column=1, sticky="ew", padx=5, pady=2)

    # Title words position dropdown
    ttk.Label(custom_title_words_section, text="Add Position:").grid(row=1, column=2, sticky="w", padx=(10, 5), pady=2)
    settings_title_words_position_var = tk.StringVar(value=config.get("custom_title_words_position", "suitable place"))
    title_words_position_combo = ttk.Combobox(
        custom_title_words_section,
        textvariable=settings_title_words_position_var,
        values=["at first", "at the end", "suitable place"],
        state="readonly",
        width=15
    )
    title_words_position_combo.grid(row=2, column=2, sticky="w", padx=(10, 5), pady=2)

    # Custom Keywords section
    custom_keywords_section = ttk.LabelFrame(scrollable_frame, text="Custom Keywords", padding="10")
    custom_keywords_section.pack(fill="x", padx=10, pady=5)
    custom_keywords_section.columnconfigure(1, weight=1)

    custom_keywords_info = ttk.Label(
        custom_keywords_section,
        text="Custom keywords that will be automatically added to generated keywords. Separate multiple keywords with commas.",
        wraplength=650
    )
    custom_keywords_info.grid(row=0, column=0, columnspan=3, sticky="w", padx=5, pady=(0, 5))

    # Keywords text area
    ttk.Label(custom_keywords_section, text="Keywords:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
    custom_keywords_text = tk.Text(custom_keywords_section, height=2, width=50)
    custom_keywords_text.insert("1.0", config.get("custom_keywords", ""))
    custom_keywords_text.grid(row=1, column=1, sticky="ew", padx=5, pady=2)

    # Keywords position dropdown
    ttk.Label(custom_keywords_section, text="Add Position:").grid(row=1, column=2, sticky="w", padx=(10, 5), pady=2)
    settings_keywords_position_var = tk.StringVar(value=config.get("custom_keywords_position", "suitable place"))
    keywords_position_combo = ttk.Combobox(
        custom_keywords_section,
        textvariable=settings_keywords_position_var,
        values=["at first", "at the end", "suitable place"],
        state="readonly",
        width=15
    )
    keywords_position_combo.grid(row=2, column=2, sticky="w", padx=(10, 5), pady=2)

    # Legacy Custom Words section (for backward compatibility)
    custom_words_section = ttk.LabelFrame(scrollable_frame, text="Legacy Custom Words (Deprecated)", padding="10")
    custom_words_section.pack(fill="x", padx=10, pady=5)
    custom_words_info = ttk.Label(
        custom_words_section,
        text="Legacy custom words (use the new Custom Title Words and Custom Keywords sections above instead).",
        wraplength=650,
        foreground="gray"
    )
    custom_words_info.pack(fill="x", pady=(0, 5))
    custom_words_text = tk.Text(custom_words_section, height=2, width=70)
    custom_words_text.insert("1.0", custom_words_var.get())
    custom_words_text.pack(fill="x", pady=5, expand=True)

    # Negative Words section
    negative_words_section = ttk.LabelFrame(scrollable_frame, text="Negative Words", padding="10")
    negative_words_section.pack(fill="x", padx=10, pady=5)
    negative_words_info = ttk.Label(
        negative_words_section,
        text="Negative words will be excluded from the generated metadata. Separate multiple words with commas.",
        wraplength=650
    )
    negative_words_info.pack(fill="x", pady=(0, 5))
    negative_words_text = tk.Text(negative_words_section, height=3, width=70) # Reduced height
    negative_words_text.insert("1.0", negative_words_var.get())
    negative_words_text.pack(fill="x", pady=5, expand=True)

    # Keyword Management section
    keyword_management_section = ttk.LabelFrame(scrollable_frame, text="Keyword Management", padding="10")
    keyword_management_section.pack(fill="x", padx=10, pady=5)
    keyword_management_section.columnconfigure(0, weight=1)
    keyword_management_section.columnconfigure(1, weight=1)

    # Info label for keyword management
    keyword_info_label = ttk.Label(
        keyword_management_section,
        text="Clean up keywords in all files currently loaded in the list. These operations will modify the keywords in the treeview.",
        wraplength=650,
        font=("Arial", 9)
    )
    keyword_info_label.grid(row=0, column=0, columnspan=2, sticky="w", padx=5, pady=(0, 10))

    # Function to remove duplicate keywords from all files
    def remove_duplicate_keywords():
        """Remove duplicate keywords from all files in the treeview."""
        if not tree.get_children():
            messagebox.showinfo("No Files", "No files found in the list to process.")
            return

        if not messagebox.askyesno("Remove Duplicates",
                                  "This will remove duplicate keywords from all files in the list. Continue?"):
            return

        processed_count = 0
        for item in tree.get_children():
            values = tree.item(item)["values"]
            if values and len(values) >= 3:
                filename, title, keywords = values[0], values[1], values[2]

                if keywords and keywords.strip():
                    # Split keywords and remove duplicates while preserving order
                    keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]
                    unique_keywords = []
                    seen = set()

                    for kw in keyword_list:
                        kw_lower = kw.lower()
                        if kw_lower not in seen:
                            unique_keywords.append(kw)
                            seen.add(kw_lower)

                    # Update if duplicates were found
                    if len(unique_keywords) != len(keyword_list):
                        new_keywords = ", ".join(unique_keywords)
                        # Update the tree item with new keywords
                        new_values = list(values)
                        new_values[2] = new_keywords
                        tree.item(item, values=tuple(new_values))
                        processed_count += 1

        messagebox.showinfo("Complete", f"Processed {processed_count} files. Duplicate keywords removed.")

    # Function to remove copyrighted keywords from all files
    def remove_copyrighted_keywords():
        """Remove copyrighted keywords from all files in the treeview."""
        if not tree.get_children():
            messagebox.showinfo("No Files", "No files found in the list to process.")
            return

        # Common copyrighted keywords to remove
        copyrighted_keywords = {
            # Major brand names
            'adobe', 'photoshop', 'illustrator', 'lightroom', 'indesign', 'acrobat', 'creative suite',
            'microsoft', 'windows', 'office', 'excel', 'word', 'powerpoint', 'outlook', 'teams',
            'apple', 'iphone', 'ipad', 'mac', 'macbook', 'ios', 'macos', 'safari', 'itunes',
            'google', 'gmail', 'chrome', 'android', 'youtube', 'maps', 'drive', 'docs',
            'facebook', 'instagram', 'whatsapp', 'meta', 'messenger',
            'twitter', 'x.com', 'tweet', 'retweet',
            'netflix', 'hulu', 'disney+', 'amazon prime', 'spotify', 'tiktok', 'snapchat',
            'linkedin', 'pinterest', 'reddit', 'tumblr', 'flickr',

            # Retail and food brands
            'amazon', 'walmart', 'target', 'costco', 'best buy', 'home depot', 'lowes',
            'starbucks', 'mcdonalds', 'burger king', 'kfc', 'pizza hut', 'dominos', 'subway',
            'coca-cola', 'pepsi', 'sprite', 'fanta', 'dr pepper', 'mountain dew',

            # Fashion and luxury brands
            'nike', 'adidas', 'puma', 'reebok', 'under armour', 'new balance',
            'gucci', 'prada', 'louis vuitton', 'chanel', 'versace', 'armani', 'dolce gabbana',
            'calvin klein', 'tommy hilfiger', 'ralph lauren', 'hugo boss', 'burberry',
            'rolex', 'omega', 'cartier', 'tiffany', 'bulgari',

            # Automotive brands
            'tesla', 'bmw', 'mercedes', 'audi', 'volkswagen', 'porsche', 'ferrari', 'lamborghini',
            'maserati', 'bentley', 'rolls royce', 'jaguar', 'land rover', 'volvo', 'saab',
            'toyota', 'honda', 'nissan', 'mazda', 'subaru', 'mitsubishi', 'hyundai', 'kia',
            'ford', 'chevrolet', 'dodge', 'jeep', 'cadillac', 'lincoln', 'buick', 'gmc',

            # Technology brands
            'samsung', 'lg', 'sony', 'panasonic', 'canon', 'nikon', 'fujifilm', 'olympus',
            'intel', 'amd', 'nvidia', 'qualcomm', 'broadcom', 'cisco', 'oracle', 'ibm',
            'dell', 'hp', 'lenovo', 'asus', 'acer', 'msi', 'alienware',
            'playstation', 'xbox', 'nintendo', 'steam', 'epic games',

            # Characters and franchises
            'mickey mouse', 'disney', 'pixar', 'marvel', 'dc comics', 'warner bros',
            'superman', 'batman', 'spiderman', 'wonder woman', 'iron man', 'captain america',
            'pokemon', 'pikachu', 'mario', 'luigi', 'zelda', 'sonic', 'pac-man',
            'star wars', 'darth vader', 'luke skywalker', 'princess leia', 'yoda', 'jedi', 'sith',
            'harry potter', 'hermione', 'ron weasley', 'dumbledore', 'voldemort', 'hogwarts',
            'lord of the rings', 'hobbit', 'gandalf', 'frodo', 'aragorn', 'legolas',
            'game of thrones', 'jon snow', 'daenerys', 'tyrion', 'westeros',
            'avengers', 'transformers', 'optimus prime', 'bumblebee',
            'barbie', 'hello kitty', 'minions', 'frozen', 'elsa', 'anna', 'olaf',

            # Sports teams and leagues
            'nfl', 'nba', 'mlb', 'nhl', 'fifa', 'uefa', 'olympics', 'ioc',
            'yankees', 'red sox', 'dodgers', 'giants', 'cubs', 'mets',
            'lakers', 'celtics', 'warriors', 'bulls', 'heat', 'knicks',
            'cowboys', 'patriots', 'packers', 'steelers', '49ers', 'eagles',
            'manchester united', 'real madrid', 'barcelona', 'chelsea', 'arsenal', 'liverpool',

            # Media and entertainment
            'hollywood', 'bollywood', 'universal studios', 'paramount', 'columbia pictures',
            'twentieth century fox', '20th century fox', 'mgm', 'lionsgate', 'dreamworks',
            'hbo', 'showtime', 'espn', 'cnn', 'bbc', 'fox news', 'msnbc',
            'mtv', 'vh1', 'comedy central', 'cartoon network', 'nickelodeon',

            # Airlines and travel
            'american airlines', 'delta', 'united airlines', 'southwest', 'jetblue',
            'lufthansa', 'british airways', 'air france', 'emirates', 'qatar airways',
            'marriott', 'hilton', 'hyatt', 'sheraton', 'holiday inn', 'best western',

            # Financial and services
            'visa', 'mastercard', 'american express', 'paypal', 'venmo', 'zelle',
            'jpmorgan', 'bank of america', 'wells fargo', 'chase', 'citibank',
            'fedex', 'ups', 'dhl', 'usps',

            # Other common copyrighted terms
            'trademark', 'copyrighted', 'registered', 'proprietary', 'licensed', 'patented',
            'all rights reserved', 'copyright', '©', '®', '™'
        }

        if not messagebox.askyesno("Remove Copyrighted Keywords",
                                  f"This will remove {len(copyrighted_keywords)} common copyrighted keywords from all files. Continue?"):
            return

        processed_count = 0
        removed_count = 0

        for item in tree.get_children():
            values = tree.item(item)["values"]
            if values and len(values) >= 3:
                filename, title, keywords = values[0], values[1], values[2]

                if keywords and keywords.strip():
                    # Split keywords and filter out copyrighted ones
                    keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]
                    original_count = len(keyword_list)

                    filtered_keywords = []
                    for kw in keyword_list:
                        if kw.lower() not in copyrighted_keywords:
                            filtered_keywords.append(kw)
                        else:
                            removed_count += 1

                    # Update if copyrighted keywords were found
                    if len(filtered_keywords) != original_count:
                        new_keywords = ", ".join(filtered_keywords)
                        # Update the tree item with new keywords
                        new_values = list(values)
                        new_values[2] = new_keywords
                        tree.item(item, values=tuple(new_values))
                        processed_count += 1

        messagebox.showinfo("Complete", f"Processed {processed_count} files. Removed {removed_count} copyrighted keywords.")

    # Remove duplicate keywords button
    remove_duplicates_button = ttk.Button(
        keyword_management_section,
        text="Remove Duplicate Keywords",
        command=remove_duplicate_keywords,
        bootstyle=INFO
    )
    remove_duplicates_button.grid(row=1, column=0, padx=5, pady=5, sticky="ew")

    # Remove copyrighted keywords button
    remove_copyrighted_button = ttk.Button(
        keyword_management_section,
        text="Remove Copyrighted Keywords",
        command=remove_copyrighted_keywords,
        bootstyle=WARNING
    )
    remove_copyrighted_button.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

    # Buttons section
    buttons_section = ttk.Frame(scrollable_frame, padding="10")
    buttons_section.pack(fill="x", pady=10)
    buttons_section.columnconfigure(0, weight=1) # For spacing
    buttons_section.columnconfigure(1, weight=1) # For spacing
    buttons_section.columnconfigure(2, weight=1) # For spacing

    # Function to save all settings
    def save_all_settings():
        # Save min/max values to config
        config["min_title_words"] = settings_min_title_var.get()
        config["max_title_words"] = settings_max_title_var.get()
        config["min_keywords"] = settings_min_keywords_var.get()
        config["max_keywords"] = settings_max_keywords_var.get()
        config["min_description_words"] = settings_min_description_var.get()
        config["max_description_words"] = settings_max_description_var.get()
        config["single_word_keywords_enabled"] = settings_single_word_keywords_var.get()
        config["batch_processing_enabled"] = settings_batch_processing_var.get()

        # Get custom prompt parts from Text widget
        custom_prompt_content = custom_prompt_text.get("1.0", "end-1c").strip()
        config["custom_prompt_parts"] = custom_prompt_content

        # Save vector embedding settings
        config["vector_embed_without_export"] = settings_vector_embed_without_export_var.get()
        config["eps_version"] = settings_eps_version_var.get()

        # Get custom title words from Text widget
        custom_title_words_content = custom_title_words_text.get("1.0", "end-1c").strip()
        config["custom_title_words"] = custom_title_words_content
        config["custom_title_words_position"] = settings_title_words_position_var.get()

        # Get custom keywords from Text widget
        custom_keywords_content = custom_keywords_text.get("1.0", "end-1c").strip()
        config["custom_keywords"] = custom_keywords_content
        config["custom_keywords_position"] = settings_keywords_position_var.get()

        # Get legacy custom words from Text widget (for backward compatibility)
        custom_words_content = custom_words_text.get("1.0", "end-1c").strip()
        config["custom_words"] = custom_words_content

        # Get negative words from Text widget
        negative_words_content = negative_words_text.get("1.0", "end-1c").strip()
        with open(NEGATIVE_KEYWORDS_FILE, "w") as f:
            f.write(negative_words_content)

        # Save config to file
        save_config(config)

        # Update main UI variables
        min_title_words_var.set(settings_min_title_var.get())
        max_title_words_var.set(settings_max_title_var.get())
        min_keywords_var.set(settings_min_keywords_var.get())
        max_keywords_var.set(settings_max_keywords_var.get())
        min_description_var.set(settings_min_description_var.get())
        max_description_var.set(settings_max_description_var.get())
        single_word_keywords_var.set(settings_single_word_keywords_var.get())


        # Update global custom words variable
        custom_words_var.set(custom_words_content)

        # Update global negative words variable
        negative_words_var.set(negative_words_content)

        messagebox.showinfo("Settings Saved", "All settings have been saved successfully!")
        settings_window.destroy()

    # Function to reset to default values
    def reset_to_defaults():
        if messagebox.askyesno("Reset Settings", "Are you sure you want to reset all settings to default values?"):
            # Default values
            settings_min_title_var.set(50)
            settings_max_title_var.set(100)
            settings_min_keywords_var.set(25)
            settings_max_keywords_var.set(30)
            settings_min_description_var.set(200)
            settings_max_description_var.set(500)
            settings_single_word_keywords_var.set(False)
            settings_batch_processing_var.set(True)

            # Reset custom prompt parts
            custom_prompt_text.delete("1.0", "end")
            custom_prompt_text.insert("1.0", "") # Default for new setting

            # Reset vector embedding settings
            settings_vector_embed_without_export_var.set(False)
            settings_eps_version_var.set("illustrator10")

            # Clear custom title words Text widget
            custom_title_words_text.delete("1.0", tk.END)
            settings_title_words_position_var.set("suitable place")

            # Clear custom keywords Text widget
            custom_keywords_text.delete("1.0", tk.END)
            settings_keywords_position_var.set("suitable place")

            # Clear legacy custom words Text widget
            custom_words_text.delete("1.0", tk.END)

            # Clear negative words Text widget
            negative_words_text.delete("1.0", tk.END)

    # Save button
    save_button = ttk.Button(
        buttons_section,
        text="Save Settings",
        command=save_all_settings,
        bootstyle=SUCCESS
    )
    save_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

    # Default button
    default_button = ttk.Button(
        buttons_section,
        text="Reset to Defaults",
        command=reset_to_defaults,
        bootstyle=WARNING
    )
    default_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

    # Cancel button
    cancel_button = ttk.Button(
        buttons_section,
        text="Cancel",
        command=settings_window.destroy,
        bootstyle=DANGER
    )
    cancel_button.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

    # Make the window modal
    settings_window.transient(root)
    settings_window.grab_set()
    root.wait_window(settings_window)

# These variables will be initialized after the root window is created
custom_words_var = None
negative_words_var = None
single_word_keywords_var = None # Initialize new global var


def save_custom_words():
    """Save the custom words to the configuration."""
    config["custom_words"] = custom_words_var.get().strip()
    save_config(config)
    messagebox.showinfo("Saved", "Custom words saved.")

def save_api_keys(api_keys):
    """Save multiple API keys to a JSON file."""
    with open(API_KEYS_FILE, "w") as f:
        json.dump(api_keys, f)

def load_api_keys():
    """Load multiple API keys from the JSON file."""
    if os.path.exists(API_KEYS_FILE):
        try:
            with open(API_KEYS_FILE, "r") as f:
                return json.load(f)
        except json.JSONDecodeError:
            print("⚠️ Error reading API keys file. Using empty list.")
            return []
    else:
        # Check if old single key file exists for migration
        if os.path.exists(API_KEY_FILE):
            try:
                with open(API_KEY_FILE, "r") as f:
                    old_key = f.read().strip()
                    if old_key:
                        # Migrate the old key to the new format
                        keys = [{"key": old_key, "label": "Migrated Key", "status": "Unknown"}]
                        save_api_keys(keys)
                        return keys
            except Exception as e:
                print(f"⚠️ Error migrating old API key: {e}")
        return []

def save_current_key_index(index):
    """Save the current API key index."""
    with open(CURRENT_KEY_INDEX_FILE, "w") as f:
        f.write(str(index))

def load_current_key_index():
    """Load the current API key index."""
    if os.path.exists(CURRENT_KEY_INDEX_FILE):
        try:
            with open(CURRENT_KEY_INDEX_FILE, "r") as f:
                return int(f.read().strip())
        except (ValueError, FileNotFoundError):
            return 0
    return 0

def load_gemini_api_key():
    """Load the current Gemini API key from the list of keys."""
    global current_api_key_index

    # Load all API keys
    api_keys = load_api_keys()

    if not api_keys:
        print("⚠️ No Gemini API keys found.")
        return None

    # Load the current key index
    current_api_key_index = load_current_key_index()

    # Make sure the index is valid
    if current_api_key_index >= len(api_keys):
        current_api_key_index = 0
        save_current_key_index(current_api_key_index)

    # Return the current key
    return api_keys[current_api_key_index]["key"]

def switch_to_next_api_key():
    """Switch to the next available API key."""
    global current_api_key_index

    # Load all API keys
    api_keys = load_api_keys()

    if not api_keys or len(api_keys) <= 1:
        print("⚠️ No alternative API keys available.")
        return None

    # Move to the next key
    current_api_key_index = (current_api_key_index + 1) % len(api_keys)
    save_current_key_index(current_api_key_index)

    # Update the key's status
    api_keys[current_api_key_index]["last_used"] = datetime.now().isoformat()
    save_api_keys(api_keys)

    print(f"🔄 Switched to API key #{current_api_key_index + 1}")
    return api_keys[current_api_key_index]["key"]

# Removed get_shutterstock_category function as its logic is now integrated
# into process_single_image for consolidated API calls.

def export_to_csv():
    """Export metadata to CSV files for all marketplaces."""
    directory = filedialog.askdirectory()
    if not directory:
        return

    marketplaces = {
        "Adobe Stock": ["Filename", "Title", "Keywords"],
        "Freepik": ["File name", "Title", "Keywords", "Prompt", "Model"],
        "Shutterstock": ["Filename", "Description", "Keywords", "Categories"],
        "Vecteezy": ["Filename", "Title", "Keywords"],
        "Dreamstime": ["filename", "title", "description", "keywords"],
        "Getty Images": ["Filename", "Title", "Description", "Keywords", "Category", "People", "Location", "Release Info"]
    }

    for marketplace, headers in marketplaces.items():
        file_path = os.path.join(directory, f"{marketplace.replace(' ', '_')}.csv")
        with open(file_path, "w", newline="", encoding='utf-8') as csvfile: # Added encoding
            writer = csv.writer(csvfile)
            writer.writerow(headers)

            for item in tree.get_children():
                values = tree.item(item, "values")
                if not values or len(values) < 6: continue # Expect 6 values now
                filename, title, keywords, description, category, rating = values # Unpack all 6

                if marketplace == "Adobe Stock":
                    writer.writerow([filename, title, keywords])
                elif marketplace == "Freepik":
                    writer.writerow([filename, title, keywords, "", ""])
                elif marketplace == "Shutterstock":
                    # Use the category already generated and stored in the tree
                    writer.writerow([filename, description, keywords, category]) # Use description for Shutterstock
                elif marketplace == "Vecteezy":
                    writer.writerow([filename, title, keywords])
                elif marketplace == "Dreamstime":
                    writer.writerow([filename, title, description, keywords]) # Use description
                elif marketplace == "Getty Images":
                    # Use the category already generated and stored in the tree
                    writer.writerow([filename, title, description, keywords, category, "", "", ""])

    messagebox.showinfo("Exported", "Data exported to CSV files for all marketplaces successfully.")

def export_to_marketplace_csv(marketplace):
    """Export metadata to a CSV file for the selected marketplace."""
    directory = filedialog.askdirectory()
    if not directory:
        return

    # Automatically generate the file name based on the marketplace
    file_path = os.path.join(directory, f"{marketplace.replace(' ', '_')}.csv")

    with open(file_path, "w", newline="", encoding='utf-8') as csvfile: # Added encoding
        writer = csv.writer(csvfile)

        # Write headers and data based on the selected marketplace
        if marketplace == "Adobe Stock":
            writer.writerow(["Filename", "Title", "Keywords"])
            for item in tree.get_children():
                values = tree.item(item, "values")
                if not values or len(values) < 6: continue
                filename, title, keywords, description, category, rating = values
                writer.writerow([filename, title, keywords])

        elif marketplace == "Freepik":
            writer.writerow(["File name", "Title", "Keywords", "Prompt", "Model"])
            for item in tree.get_children():
                values = tree.item(item, "values")
                if not values or len(values) < 6: continue
                filename, title, keywords, description, category, rating = values
                writer.writerow([filename, title, keywords, "Generated Prompt", ""])

        elif marketplace == "Shutterstock":
            writer.writerow(["Filename", "Description", "Keywords", "Categories"])
            for item in tree.get_children():
                values = tree.item(item, "values")
                if not values or len(values) < 6: continue
                filename, title, keywords, description, category, rating = values
                writer.writerow([filename, description, keywords, category]) # Use description

        elif marketplace == "Vecteezy":
            writer.writerow(["Filename", "Title", "Keywords"])
            for item in tree.get_children():
                values = tree.item(item, "values")
                if not values or len(values) < 6: continue
                filename, title, keywords, description, category, rating = values
                writer.writerow([filename, title, keywords])

        elif marketplace == "Dreamstime":
            writer.writerow(["filename", "title", "description", "keywords"])
            for item in tree.get_children():
                values = tree.item(item, "values")
                if not values or len(values) < 6: continue
                filename, title, keywords, description, category, rating = values
                writer.writerow([filename, title, description, keywords])

        elif marketplace == "Getty Images":
            writer.writerow(["Filename", "Title", "Description", "Keywords", "Category", "People", "Location", "Release Info"])
            for item in tree.get_children():
                values = tree.item(item, "values")
                if not values or len(values) < 6: continue
                filename, title, keywords, description, category, rating = values
                writer.writerow([filename, title, description, keywords, category, "", "", ""])

    messagebox.showinfo("Exported", f"Data exported to {file_path} successfully.")

def toggle_theme(theme):
    """Toggle the UI theme while preserving column weights and sizes."""
    # Change the theme
    root.style.theme_use(theme)

    # Preserve the column weights in the combined frame
    combined_frame.columnconfigure(0, weight=1)

    # Force update of the UI
    root.update_idletasks()

    # Reset the minimum size of the treeview frame
    treeview_scroll_frame.update_idletasks()

    # Set a minimum width for the treeview frame
    min_width = 950  # Minimum width in pixels
    treeview_scroll_frame.grid_configure(minsize=(min_width, 0))

def retry_failed_files():
    """Identify and retry files that have errors or blank/pending metadata."""
    files_to_retry = []
    items_to_reprocess_count = 0

    for item_id in tree.get_children():
        values = tree.item(item_id, "values")
        if not values or len(values) < 6:  # Expect 6 values (Filename, Title, Keywords, Description, Category, Rating)
            continue

        filename, title, keywords, description, category_val, rating_val = values # Unpack all 6
        
        # Check for error conditions or pending/blank metadata
        is_error = "error" in title.lower() or "error" in keywords.lower() or "error" in description.lower()
        is_pending = title == "Pending..." or keywords == "Pending..." or description == "Pending..."
        is_blank = not title.strip() or not keywords.strip() # Description can be intentionally blank if disabled

        if is_error or is_pending or is_blank:
            items_to_reprocess_count += 1
            # Find the full path in the image_listbox
            original_file_path = None
            for i in range(image_listbox.size()):
                if os.path.basename(image_listbox.get(i)) == filename:
                    original_file_path = image_listbox.get(i)
                    break
            
            if original_file_path:
                files_to_retry.append(original_file_path)
            else:
                print(f"Warning: Could not find original path for file: {filename} marked for retry.")

    if not files_to_retry:
        if items_to_reprocess_count > 0:
             messagebox.showinfo("Files Not Found", f"Found {items_to_reprocess_count} items with errors or pending metadata, but couldn't locate their original paths for reprocessing.")
        else:
            messagebox.showinfo("No Files to Retry", "No files with errors or pending/blank metadata were found to retry.")
        return

    if not messagebox.askyesno("Retry Files", f"Found {len(files_to_retry)} files with errors or incomplete metadata. Do you want to reprocess them?"):
        return

    status_message_label.config(text=f"Retrying {len(files_to_retry)} failed/pending files...", foreground="#008000")
    process_images(files_to_retry)

def pause_processing():
    # Only allow pause/resume if processing is active
    if not processing_active.get():
        return

    if pause_event.is_set():
        # Pausing the process
        pause_event.clear()
        pause_button.config(text="Resume", bootstyle=SUCCESS)
        status_message_label.config(text="Process paused", foreground="#FFA500")
    else:
        # Resuming the process
        pause_event.set()
        pause_button.config(text="Pause", bootstyle=WARNING)
        status_message_label.config(text="Processing files...", foreground="#008000")

def open_contact_us():
    import webbrowser
    webbrowser.open("https://www.facebook.com/m.mastersoft/")

def open_get_api_link():
    import webbrowser
    webbrowser.open("https://aistudio.google.com/apikey")

def resource_path(relative_path):
    """Get the absolute path to a resource, works for both development and PyInstaller EXE."""
    if getattr(sys, 'frozen', False):  # Running as a bundled EXE
        base_path = sys._MEIPASS
    else:  # Running as a script
        base_path = os.path.dirname(__file__)
    return os.path.join(base_path, relative_path)

# Fix Firebase JSON issue
firebase_json_path = resource_path("meta-master-firebase.json")

SCOPES = ["https://www.googleapis.com/auth/cloud-platform"]
SERVICE_ACCOUNT_FILE = resource_path("meta-master-firebase.json")

credentials = service_account.Credentials.from_service_account_file(
    SERVICE_ACCOUNT_FILE,
    scopes=SCOPES
)
authorized_session = AuthorizedSession(credentials)

# Extract project_id from the service account file
with open(SERVICE_ACCOUNT_FILE, "r") as f:
    service_account_data = json.load(f)
project_id = service_account_data["project_id"]

# Initialize Firestore with refreshed credentials and project_id
if not firebase_admin._apps:
    firebase_admin.initialize_app(credentials, {"projectId": project_id})
db = firestore.client()

LICENSE_FILE = os.path.join(os.getenv("APPDATA"), "MetaMaster", "license.txt")
  # License key storage file

def load_license_key():
    """Retrieve the saved license key from the writable AppData location."""
    if os.path.exists(LICENSE_FILE):
        with open(LICENSE_FILE, "r") as f:
            return f.read().strip()
    return None

def disable_all_buttons():
    """Disable all buttons in the application."""
    for widget in root.winfo_children():
        if isinstance(widget, ttk.Button):
            widget.config(state=tk.DISABLED)
    # Disable other interactive widgets
    image_listbox.config(state=tk.DISABLED)
import time
from collections import deque
from datetime import datetime, timedelta

# Enhanced rate limiting configuration
RATE_LIMIT_WINDOW = 60  # 1 minute window
MAX_REQUESTS = 45       # Increased from 10 - Allows more requests per minute
BURST_LIMIT = 5         # Increased from 3 - Allows a slightly larger burst
MIN_DELAY = 0.05        # Reduced from 0.1 - Shorter minimal delay
BATCH_SIZE = 5          # Increased from 3 - Process more items before strict rate limiting
request_timestamps = deque(maxlen=MAX_REQUESTS) # maxlen will use the new MAX_REQUESTS
batch_counter = 0  # Counter for batch processing

def check_rate_limit():
    """Enhanced rate limiting with batch processing and adaptive delays."""
    global batch_counter
    now = datetime.now()

    # Batch processing: allow a few requests to go through without delay
    if batch_counter < BATCH_SIZE:
        batch_counter += 1
        request_timestamps.append(now)
        return True, 0
    else:
        batch_counter = 0  # Reset batch counter

    # Remove timestamps older than our window
    while request_timestamps and (now - request_timestamps[0]) > timedelta(seconds=RATE_LIMIT_WINDOW):
        request_timestamps.popleft()

    # Calculate request density (requests per second in recent history)
    if request_timestamps:
        recent_window = 10  # Look at last 10 seconds
        recent_count = sum(1 for ts in request_timestamps if (now - ts).total_seconds() < recent_window)
        request_density = recent_count / recent_window if recent_window > 0 else 0
    else:
        request_density = 0

    # If we have capacity and density is low, proceed immediately
    if len(request_timestamps) < MAX_REQUESTS * 0.8 and request_density < 0.5:
        request_timestamps.append(now)
        return True, 0

    # If we're approaching limits, add adaptive delay
    if len(request_timestamps) < MAX_REQUESTS:
        # Calculate delay based on request density and current usage
        usage_factor = len(request_timestamps) / MAX_REQUESTS
        density_factor = min(request_density, 1.0)

        # Adaptive delay increases as we approach limits
        delay = MIN_DELAY * (1 + usage_factor * 5) * (1 + density_factor * 2)

        # Add the timestamp after the delay
        request_timestamps.append(now)
        return False, min(delay, 1.0)  # Cap delay at 1 second

    # We're at capacity, calculate wait time
    oldest_timestamp = request_timestamps[0]
    wait_time = (oldest_timestamp + timedelta(seconds=RATE_LIMIT_WINDOW) - now).total_seconds()

    # Use a shorter wait time with adaptive adjustment
    usage_ratio = len(request_timestamps) / MAX_REQUESTS
    adjustment_factor = 0.5 + (usage_ratio * 0.3)  # 50-80% of wait time
    adjusted_wait_time = max(0.2, wait_time * adjustment_factor)

    return False, min(3.0, adjusted_wait_time)  # Cap at 3 seconds for better UX

# This function is duplicated, removing the second instance.
# def generate_description_with_gemini(file_path, title, keywords):
#     ... (already defined above) ...

def enable_all_buttons():
    """Enable all buttons in the application."""
    for widget in root.winfo_children():
        if isinstance(widget, ttk.Button):
            widget.config(state=tk.NORMAL)
    # Enable other interactive widgets
    image_listbox.config(state=tk.NORMAL)
    mode_var.set("Image")

    # Update API key status
    update_api_key_status()

def get_license_info():
    """Fetch license key details from Firebase."""
    license_key = load_license_key()
    if not license_key:
        return "❌ No License Found", "Please enter a valid license key.", False

    doc_ref = db.collection("licenses").document(license_key)
    doc = doc_ref.get()

    if doc.exists:
        license_data = doc.to_dict()
        expiry_date = license_data.get("expiry")
        is_active = license_data.get("active", False)

        # Convert expiry date to datetime object
        expiry_date_dt = datetime.strptime(expiry_date, "%Y-%m-%d")
        today = datetime.today()
        remaining_days = (expiry_date_dt - today).days

        if not is_active:
            return "❌ License Deactivated", "Your license has been disabled. Contact support.", False

        if remaining_days > 0:
            if remaining_days <= 7:
                return "⚠️ License Expiring Soon!", f"🔴 {remaining_days} days left. Please renew soon!", True
            return "✅ License Active", f"🔹 Expires on: {expiry_date} ({remaining_days} days left)", True
        else:
            return "❌ License Expired", "Your license has expired. Please renew.", False
    else:
        return "❌ Invalid License", "The license key is not recognized.", False

def show_license_status():
    """Fetch and display license status with color indication."""
    status, message, is_valid = get_license_info()

    # Set label text
    root.after(0, lambda: license_status_label.config(text=status))
    root.after(0, lambda: license_message_label.config(text=message))

    # Change color dynamically
    if "License Active" in status:
        root.after(0, lambda: license_status_label.config(foreground="green"))  # Set to green if active
    elif "License Expired" in status or "License Deactivated" in status:
        root.after(0, lambda: license_status_label.config(foreground="red"))  # Set to red if expired
    else:
        root.after(0, lambda: license_status_label.config(foreground="orange"))  # Set to orange for warnings

    if not is_valid:
        root.after(0, lambda: Messagebox.show_error(message, "License Issue"))
        root.after(1000, lambda: root.quit())  # Quit safely instead of sys.exit()

# This function is duplicated, removing the second instance.
# def show_license_input_window():
#     ... (already defined above) ...


def check_and_show_license():
    """Check if the license is valid and show the input window if not."""
    status, message, is_valid = get_license_info()
    if not is_valid:
        show_license_input_window() # This will use the first definition
        return False
    return True

# Check license before initializing the main application window
if not check_and_show_license():
    sys.exit()

def launch_main_software():
    """Launch the main software UI after a valid license is found."""
    global root
    root = ttk.Window(themename="superhero")  # Ensure only one root instance
    root.title("Meta Master 5.3.0")
    root.geometry("800x600")

    # Load and set application icon
    icon_path_ico = resource_path("Meta Master.ico")  # Ensure correct path to the icon file
    if os.path.exists(icon_path_ico): # Renamed variable
        root.iconbitmap(icon_path_ico)  # Set the icon for the top bar
    else:
        print(f"❌ Icon file not found at: {icon_path_ico}")


    # Force Taskbar Icon to Show Correctly
    icon_path_png = resource_path("Meta Master.png") # Renamed variable
    if os.path.exists(icon_path_png):
        icon = PhotoImage(file=icon_path_png)  # PNG works better for the taskbar
        root.tk.call("wm", "iconphoto", root._w, icon)
    else:
        print(f"❌ Icon file (PNG) not found at: {icon_path_png}")


    # Add your UI components here...

    show_license_status()  # Check the license after creating the UI
    root.mainloop()  # Start Tkinter event loop

# Determine the correct file path for bundled files
if getattr(sys, 'frozen', False):  # Running from .exe
    base_path = sys._MEIPASS  # Temporary extraction folder
else:  # Running from .py script
    base_path = os.path.dirname(__file__)

# Ensure the root window is created first
root = ttk.Window(themename="superhero")
root.title("Meta Master 5.3.0")
root.geometry("800x600")

# Load and set application icon for the root window
icon_path_ico_root = resource_path("Meta Master.ico")  # Renamed variable
if os.path.exists(icon_path_ico_root):
    root.iconbitmap(icon_path_ico_root)  # Set the icon for the top bar
else:
    print(f"❌ Icon file not found at: {icon_path_ico_root}")


# Create a BooleanVar to track if processing is active
processing_active = tk.BooleanVar(value=False)

# Create a BooleanVar for description toggle
description_enabled = tk.BooleanVar(value=True)

# Initialize global variables for settings
custom_words_var = tk.StringVar(value=config.get("custom_words", ""))
negative_words_var = tk.StringVar(value=",".join(load_negative_keywords()))
single_word_keywords_var = tk.BooleanVar(value=config.get("single_word_keywords_enabled", False))


# Now create UI elements
main_frame = ttk.Frame(root, padding=(15, 5, 15, 5))
main_frame.pack(fill="both", expand=True)

# Professional branding header
branding_frame = ttk.Frame(main_frame, padding=(0, 2, 0, 5)) # Added padding: left, top, right, bottom
branding_frame.pack(fill=tk.X, pady=(0, 10)) # Increased bottom padding from main_frame
branding_frame.columnconfigure(0, weight=1) # Allow left part (branding_left) to expand
branding_frame.columnconfigure(1, weight=0) # Keep right part (api_key_frame) fixed size

# Left side: Logo and branding
branding_left = ttk.Frame(branding_frame)
branding_left.grid(row=0, column=0, sticky="w", padx=(0,10)) # Added some right padding

# Correctly load the branding PNG image
icon_path_png_brand = resource_path("Meta Master.png") # Renamed variable


# Add logo to the branding
try:
    logo_image = tk.PhotoImage(file=icon_path_png_brand)  # Load image
    logo_label = ttk.Label(branding_left, image=logo_image)
    logo_label.pack(side=tk.LEFT, padx=(0, 5))
except Exception as e:
    print(f"❌ Error loading image: {e}")

# Title and developer info in a vertical stack
branding_text = ttk.Frame(branding_left)
branding_text.pack(side=tk.LEFT, fill=tk.Y)

branding_label = ttk.Label(branding_text, text="Meta Master", font=("Helvetica", 20, "bold"))
branding_label.pack(side=tk.TOP, anchor="w")

developer_label = ttk.Label(branding_text, text="Developed By Jayed Ahmed", font=("Helvetica", 9))
developer_label.pack(side=tk.TOP, anchor="w")

# Right side: API key management section
api_key_frame = ttk.Frame(branding_frame)
api_key_frame.grid(row=0, column=1, sticky="e", pady=(5,0)) # Align to the top-right, add top padding

# API Key status indicator - using pack within its own sub-frame for better control
api_key_status_display_frame = ttk.Frame(api_key_frame) # New frame for status text
api_key_status_display_frame.pack(side=tk.LEFT, padx=(0, 10), anchor="center")

api_key_status_label = ttk.Label(api_key_status_display_frame, text="API Key:", font=("Helvetica", 9))
api_key_status_label.pack(side=tk.LEFT, padx=(0, 3))

# Status indicator (will be updated when keys are loaded)
api_key_status_indicator = ttk.Label(api_key_status_display_frame, text="⚠️ Not Set", foreground="orange", font=("Helvetica", 9, "bold"))
api_key_status_indicator.pack(side=tk.LEFT)

# Frame for API buttons, packed to the right of status
api_buttons_frame = ttk.Frame(api_key_frame)
api_buttons_frame.pack(side=tk.LEFT, anchor="center")

# Add button to manage API keys
manage_keys_button = ttk.Button(
    api_buttons_frame, # Changed parent
    text="Manage Keys", # Shorter text
    command=manage_api_keys,
    bootstyle="primary-outline", # Outline style
    width=14 # Adjusted width
)
manage_keys_button.pack(side=tk.LEFT, padx=(0, 5)) # pady handled by parent api_key_frame

# Add button to get API key
get_key_button = ttk.Button(api_buttons_frame, text="Get Key", command=open_get_api_link, bootstyle="info-outline", width=8) # Shorter text & outline
get_key_button.pack(side=tk.LEFT, padx=(0, 5)) # pady handled by parent api_key_frame

# Function to update API key status indicator
def update_api_key_status():
    """Update the API key status indicator based on available keys."""
    api_keys = load_api_keys()

    if not api_keys:
        api_key_status_indicator.config(text="⚠️ Not Set", foreground="orange")
        print("❌ No API keys found")
        return False

    # Check if the current key is valid
    current_key = load_gemini_api_key()
    if current_key:
        try:
            print(f"🔑 Checking API key status...")
            # Quick validation without showing messages
            genai.configure(api_key=current_key)
            model = genai.GenerativeModel('gemini-2.5-flash-lite')
            # Just configure, don't make a request to avoid rate limits
            api_key_status_indicator.config(text="✅ Ready", foreground="green")
            print(f"✅ API key is valid and ready")
            return True
        except Exception as e:
            print(f"❌ API key validation error: {str(e)}")
            if "403" in str(e):
                api_key_status_indicator.config(text="❌ Invalid", foreground="red")
            elif "429" in str(e):
                api_key_status_indicator.config(text="⚠️ Rate Limited", foreground="orange")
                # We have keys but current one is rate limited
                return True
            else:
                api_key_status_indicator.config(text="⚠️ Error", foreground="orange")
            return False
    else:
        print(f"❌ No current API key found")
        api_key_status_indicator.config(text="⚠️ Not Set", foreground="orange")
        return False

def test_api_connection():
    """Test the API connection with a simple request and show the result."""
    try:
        print("🧪 Testing API connection...")
        api_key = load_gemini_api_key()
        if not api_key:
            messagebox.showerror("API Error", "No API key found. Please add an API key in the settings.")
            print("❌ No API key found for testing")
            return

        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-flash-lite')

        # Make a simple test request
        start_time = time.time()
        response = model.generate_content("Hello, this is a test request to check if the API is working.")
        end_time = time.time()

        if response and response.text:
            elapsed = round(end_time - start_time, 2)
            messagebox.showinfo("API Test Successful",
                               f"API connection is working!\n\nResponse: {response.text[:100]}...\n\nTime taken: {elapsed} seconds")
            print(f"✅ API test successful in {elapsed}s: {response.text[:50]}...")
        else:
            messagebox.showerror("API Test Failed", "API connection test failed. No response received.")
            print("❌ API test failed: No response received")
    except Exception as e:
        error_message = str(e)
        messagebox.showerror("API Test Failed", f"API connection test failed with error:\n\n{error_message}")
        print(f"❌ API test failed with error: {error_message}")

# Add button to test API connection (after function definition)
test_api_button = ttk.Button(api_buttons_frame, text="Test API", command=test_api_connection, bootstyle="success-outline", width=8)
test_api_button.pack(side=tk.LEFT, padx=(0, 0))

# Check API key status
has_valid_keys = update_api_key_status()
if not has_valid_keys:
    # Show warning only if no valid keys are available
    messagebox.showwarning("API Key Required", "Please add at least one valid API key to use the software.")

# --- Row 1: Mode, Theme, Primary Toggles, Video Options, and App Actions ---
controls_row1_frame = ttk.Frame(main_frame)
controls_row1_frame.pack(fill=tk.X, pady=(5, 3)) 

# Mode Selection
mode_select_frame = ttk.LabelFrame(controls_row1_frame, text="Mode", padding=(5,3))
mode_select_frame.pack(side=tk.LEFT, padx=(0, 5), fill=tk.Y, anchor="n")

mode_var = tk.StringVar(value="Image") 
image_radio = ttk.Radiobutton(mode_select_frame, text="Image", variable=mode_var, value="Image", command=toggle_mode, bootstyle="success-toolbutton")
image_radio.pack(side=tk.LEFT, padx=3, pady=3)
vector_radio = ttk.Radiobutton(mode_select_frame, text="Vector", variable=mode_var, value="Vector", command=toggle_mode, bootstyle="success-toolbutton")
vector_radio.pack(side=tk.LEFT, padx=3, pady=3)
video_radio = ttk.Radiobutton(mode_select_frame, text="Video", variable=mode_var, value="Video", command=toggle_mode, bootstyle="success-toolbutton")
video_radio.pack(side=tk.LEFT, padx=3, pady=3)
prompt_radio = ttk.Radiobutton(mode_select_frame, text="Prompt Generator", variable=mode_var, value="Prompt Generator", command=toggle_mode, bootstyle="success-toolbutton")
prompt_radio.pack(side=tk.LEFT, padx=3, pady=3)

# Theme Selection
theme_select_frame = ttk.LabelFrame(controls_row1_frame, text="Theme", padding=(5,3))
theme_select_frame.pack(side=tk.LEFT, padx=(0, 5), fill=tk.Y, anchor="n")

dark_theme_button = ttk.Button(theme_select_frame, text="Dark", command=lambda: toggle_theme("darkly"), bootstyle="secondary-outline", width=5)
dark_theme_button.pack(side=tk.LEFT, padx=3, pady=3)
light_theme_button = ttk.Button(theme_select_frame, text="Light", command=lambda: toggle_theme("morph"), bootstyle="secondary-outline", width=5)
light_theme_button.pack(side=tk.LEFT, padx=3, pady=3)
system_theme_button = ttk.Button(theme_select_frame, text="System", command=lambda: toggle_theme("superhero"), bootstyle="secondary-outline", width=6)
system_theme_button.pack(side=tk.LEFT, padx=3, pady=3)

# Primary Toggles Frame
primary_toggles_frame = ttk.LabelFrame(controls_row1_frame, text="Generation Options", padding=(5,3))
primary_toggles_frame.pack(side=tk.LEFT, padx=(0, 5), fill=tk.Y, anchor="n")

# Add a square toggle button for description generation
desc_toggle_frame = ttk.Frame(primary_toggles_frame) 
desc_toggle_frame.pack(side=tk.LEFT, padx=(0, 3), pady=0, anchor="center")

desc_toggle_btn = ttk.Checkbutton(
    desc_toggle_frame,
    text="Description",
    variable=description_enabled,
    bootstyle="square-toggle,outline-primary",
    onvalue=True,
    offvalue=False,
    width=12
)
desc_toggle_btn.pack(side=tk.LEFT)

# Add a toggle button for filename hint processing
filename_hint_enabled = tk.BooleanVar(value=config.get("filename_hint_enabled", True))  # Default to enabled
filename_hint_frame = ttk.Frame(primary_toggles_frame) 
filename_hint_frame.pack(side=tk.LEFT, padx=(0, 3), pady=0, anchor="center")

filename_hint_btn = ttk.Checkbutton(
    filename_hint_frame,
    text="Filename Hint",
    variable=filename_hint_enabled,
    bootstyle="square-toggle,outline-success",
    onvalue=True,
    offvalue=False,
    width=12,
    command=lambda: save_filename_hint_setting()
)
filename_hint_btn.pack(side=tk.LEFT)

# Add a toggle button for auto-embedding metadata
auto_embed_enabled = tk.BooleanVar(value=config.get("auto_embed_enabled", True))  # Default to enabled
auto_embed_frame = ttk.Frame(primary_toggles_frame)
auto_embed_frame.pack(side=tk.LEFT, padx=(0, 3), pady=0, anchor="center")

auto_embed_btn = ttk.Checkbutton(
    auto_embed_frame,
    text="Auto Embed",
    variable=auto_embed_enabled,
    bootstyle="square-toggle,outline-success" if auto_embed_enabled.get() else "square-toggle,outline-warning",
    onvalue=True,
    offvalue=False,
    width=12,
    command=lambda: save_auto_embed_setting()
)
auto_embed_btn.pack(side=tk.LEFT)

# Add status indicator for auto embed
auto_embed_status_label = ttk.Label(
    auto_embed_frame,
    text="✅" if auto_embed_enabled.get() else "⚠️",
    foreground="green" if auto_embed_enabled.get() else "orange"
)
auto_embed_status_label.pack(side=tk.LEFT, padx=(2, 0))

def save_auto_embed_setting():
    """Save the auto embed setting to the configuration."""
    config["auto_embed_enabled"] = auto_embed_enabled.get()
    save_config(config)

    # Update status indicator
    if auto_embed_enabled.get():
        auto_embed_status_label.config(text="✅", foreground="green")
        auto_embed_btn.config(bootstyle="square-toggle,outline-success")
        print("✅ Auto Embed enabled")
    else:
        auto_embed_status_label.config(text="⚠️", foreground="orange")
        auto_embed_btn.config(bootstyle="square-toggle,outline-warning")
        print("⚠️ Auto Embed disabled")

# Ensure auto embed is enabled by default if not set in config
if "auto_embed_enabled" not in config:
    config["auto_embed_enabled"] = True
    auto_embed_enabled.set(True)
    save_config(config)
    print("🔧 Auto Embed enabled by default")

# Set initial state of auto embed button based on current mode
# Enable for Image and Vector modes, disable for Video mode
if mode_var.get() not in ["Image", "Vector"]:
    if 'auto_embed_btn' in globals():
        auto_embed_btn.config(state=tk.DISABLED)

def force_enable_auto_embed():
    """Force enable auto embed if it's disabled."""
    if not auto_embed_enabled.get():
        auto_embed_enabled.set(True)
        save_auto_embed_setting()
        print("🔧 Auto Embed force-enabled")

def save_filename_hint_setting():
    """Save the filename hint setting to the configuration."""
    config["filename_hint_enabled"] = filename_hint_enabled.get()
    save_config(config)

# Add a toggle button for PNG "isolated on a white background"
png_isolated_enabled = tk.BooleanVar(value=config.get("png_isolated_enabled", False))
png_isolated_frame = ttk.Frame(primary_toggles_frame) 
png_isolated_frame.pack(side=tk.LEFT, padx=(0, 3), pady=0, anchor="center")

def save_png_isolated_setting():
    config["png_isolated_enabled"] = png_isolated_enabled.get()
    save_config(config)

png_isolated_btn = ttk.Checkbutton(
    png_isolated_frame,
    text="PNG Isolated",
    variable=png_isolated_enabled,
    bootstyle="square-toggle,outline-warning",
    onvalue=True,
    offvalue=False,
    width=14,
    command=save_png_isolated_setting
)
png_isolated_btn.pack(side=tk.LEFT)

# Add a toggle button for PNG background refinement
png_refine_transparent_enabled = tk.BooleanVar(value=config.get("png_refine_transparent_enabled", False))
png_refine_frame = ttk.Frame(primary_toggles_frame) 
png_refine_frame.pack(side=tk.LEFT, padx=(0, 3), pady=0, anchor="center")

def save_png_refine_setting():
    config["png_refine_transparent_enabled"] = png_refine_transparent_enabled.get()
    save_config(config)

png_refine_btn = ttk.Checkbutton(
    png_refine_frame,
    text="Refine PNG BG", # Shorter text for the button
    variable=png_refine_transparent_enabled,
    bootstyle="square-toggle,outline-success", # Different color
    onvalue=True,
    offvalue=False,
    width=15, # Adjusted width
    command=save_png_refine_setting
)
png_refine_btn.pack(side=tk.LEFT)

def check_png_files_selected():
    """Check if any PNG files are selected and enable/disable PNG options accordingly."""
    has_png_files = False

    # Check if any PNG files are in the image_listbox
    for i in range(image_listbox.size()):
        file_path = image_listbox.get(i)
        if file_path.lower().endswith('.png'):
            has_png_files = True
            break

    # Enable or disable PNG buttons based on PNG file presence
    if has_png_files:
        png_isolated_btn.config(state=tk.NORMAL)
        png_refine_btn.config(state=tk.NORMAL)
        print("🔧 PNG files detected - PNG options enabled")
    else:
        png_isolated_btn.config(state=tk.DISABLED)
        png_refine_btn.config(state=tk.DISABLED)
        print("🔧 No PNG files detected - PNG options disabled")

    return has_png_files

# Initialize PNG buttons as disabled (will be enabled when PNG files are selected)
png_isolated_btn.config(state=tk.DISABLED)
png_refine_btn.config(state=tk.DISABLED)

# Create variables for all scales with default values
min_title_words_var = tk.IntVar(value=config.get("min_title_words", 50))
max_title_words_var = tk.IntVar(value=config.get("max_title_words", 100))
min_keywords_var = tk.IntVar(value=config.get("min_keywords", 25))
max_keywords_var = tk.IntVar(value=config.get("max_keywords", 30))
min_description_var = tk.IntVar(value=config.get("min_description_words", 200))
max_description_var = tk.IntVar(value=config.get("max_description_words", 500))

# Video Options (conditionally shown - will be packed/unpacked in toggle_mode)
video_options_frame = ttk.LabelFrame(controls_row1_frame, text="Video", padding=(5,3))
# video_options_frame is packed by toggle_mode when Video mode is selected

frame_count_label = ttk.Label(video_options_frame, text="Frames:", font=("Arial", 8)) 
frame_count_label.pack(side=tk.LEFT, padx=(5, 2), pady=3)
frame_count_var = tk.IntVar(value=4)  # Default 4 frames
frame_count_entry = ttk.Entry(video_options_frame, textvariable=frame_count_var, width=2) 
frame_count_entry.pack(side=tk.LEFT, padx=(0,5), pady=3)

# App Actions Frame (Settings, Contact Us) - ensure it's the last on the right of this row
app_actions_frame = ttk.LabelFrame(controls_row1_frame, text="Application", padding=(5,3))
app_actions_frame.pack(side=tk.LEFT, padx=(0,0), fill=tk.Y, anchor="n") 

settings_button = ttk.Button(app_actions_frame, text="Settings", command=open_settings_window, bootstyle="info-outline", width=8)
settings_button.pack(side=tk.LEFT, padx=3, pady=3)
contact_us_button = ttk.Button(app_actions_frame, text="Contact", command=open_contact_us, bootstyle="info-outline", width=8) 
contact_us_button.pack(side=tk.LEFT, padx=3, pady=3)




# --- Row 2: File Input, Processing Controls, and Utilities ---
controls_row2_frame = ttk.Frame(main_frame)
controls_row2_frame.pack(fill=tk.X, pady=(3, 5)) # Reduced top padding

# File Input Section
file_input_frame = ttk.LabelFrame(controls_row2_frame, text="Input", padding=(5,3))
file_input_frame.pack(side=tk.LEFT, padx=(0, 5), fill=tk.Y, anchor="n")

select_button = ttk.Button(file_input_frame, text="SELECT FILES", command=select_images, bootstyle="primary")
select_button.pack(side=tk.LEFT, padx=3, pady=3)
select_folder_button = ttk.Button(file_input_frame, text="SELECT FOLDER", command=select_folder, bootstyle="primary")
select_folder_button.pack(side=tk.LEFT, padx=3, pady=3)

# Processing Controls Section
processing_controls_frame = ttk.LabelFrame(controls_row2_frame, text="Processing", padding=(5,3))
processing_controls_frame.pack(side=tk.LEFT, padx=(0, 5), fill=tk.Y, anchor="n")

start_button = ttk.Button(processing_controls_frame, text="Start", command=lambda: process_images(image_listbox.get(0, tk.END)), bootstyle=SUCCESS, width=7)
start_button.pack(side=tk.LEFT, padx=3, pady=3)
pause_button = ttk.Button(processing_controls_frame, text="Pause", command=pause_processing, bootstyle=WARNING, width=7)
pause_button.pack(side=tk.LEFT, padx=3, pady=3)
retry_button = ttk.Button(processing_controls_frame, text="Retry", command=retry_failed_files, bootstyle=INFO, width=7)
retry_button.pack(side=tk.LEFT, padx=3, pady=3)
clear_button = ttk.Button(processing_controls_frame, text="Clear", command=clear_data, bootstyle=DANGER, width=7)
clear_button.pack(side=tk.LEFT, padx=3, pady=3)

# Export Controls Section
export_controls_frame = ttk.LabelFrame(controls_row2_frame, text="Export", padding=(5,3))
export_controls_frame.pack(side=tk.LEFT, padx=(0, 5), fill=tk.Y, anchor="n")

marketplace_label = ttk.Label(export_controls_frame, text="Marketplace:")
marketplace_label.pack(side=tk.LEFT, padx=(5,2), pady=3)
marketplace_options = ["All Marketplace", "Adobe Stock", "Freepik", "Shutterstock", "Vecteezy", "Dreamstime", "Getty Images"]
marketplace_var = tk.StringVar(value="All Marketplace")
marketplace_dropdown = ttk.Combobox(export_controls_frame, textvariable=marketplace_var, values=marketplace_options, state="readonly", width=15)
marketplace_dropdown.pack(side=tk.LEFT, padx=(0,5), pady=3)

# Modify the export button to handle specific or all marketplaces
def export_selected_marketplace():
    """Export metadata to CSV for the selected marketplace or all marketplace."""
    selected_marketplace = marketplace_var.get()
    if selected_marketplace == "All Marketplace":
        export_to_csv()  # Export for all marketplaces
    else:
        export_to_marketplace_csv(selected_marketplace)  # Export for the selected marketplace

export_all_button = ttk.Button(export_controls_frame, text="Export CSV", command=export_selected_marketplace, bootstyle="info-outline", width=10)
export_all_button.pack(side=tk.LEFT, padx=3, pady=3)

# File Utilities Section
file_utils_frame = ttk.LabelFrame(controls_row2_frame, text="Utilities", padding=(5,3))
file_utils_frame.pack(side=tk.LEFT, padx=(0, 0), fill=tk.Y, anchor="n") # Last frame on this row

def find_replace_metadata():
    """Open a dialog to find and replace text in all metadata fields."""
    if not tree.get_children():
        messagebox.showerror("Error", "No metadata available to search and replace.")
        return

    # Create a new window for find and replace
    find_replace_window = ttk.Toplevel(root)
    find_replace_window.title("Find and Replace Metadata")
    find_replace_window.geometry("500x400")
    find_replace_window.resizable(True, True)

    # Create main frame
    main_frame = ttk.Frame(find_replace_window, padding="10")
    main_frame.pack(fill="both", expand=True)

    # Find and replace fields
    find_frame = ttk.LabelFrame(main_frame, text="Find and Replace", padding="10")
    find_frame.pack(fill="x", pady=(0, 10))

    # Find field
    find_label = ttk.Label(find_frame, text="Find:")
    find_label.grid(row=0, column=0, sticky="w", padx=(0, 5), pady=5)

    find_entry = ttk.Entry(find_frame, width=40)
    find_entry.grid(row=0, column=1, sticky="we", padx=5, pady=5)

    # Replace field
    replace_label = ttk.Label(find_frame, text="Replace with:")
    replace_label.grid(row=1, column=0, sticky="w", padx=(0, 5), pady=5)

    replace_entry = ttk.Entry(find_frame, width=40)
    replace_entry.grid(row=1, column=1, sticky="we", padx=5, pady=5)

    # Options frame
    options_frame = ttk.LabelFrame(main_frame, text="Options", padding="10")
    options_frame.pack(fill="x", pady=(0, 10))

    # Checkboxes for which fields to search
    search_title_var = tk.BooleanVar(value=True)
    search_title_check = ttk.Checkbutton(
        options_frame,
        text="Search in Titles",
        variable=search_title_var,
        bootstyle="round-toggle"
    )
    search_title_check.grid(row=0, column=0, sticky="w", padx=5, pady=2)

    search_keywords_var = tk.BooleanVar(value=True)
    search_keywords_check = ttk.Checkbutton(
        options_frame,
        text="Search in Keywords",
        variable=search_keywords_var,
        bootstyle="round-toggle"
    )
    search_keywords_check.grid(row=1, column=0, sticky="w", padx=5, pady=2)

    search_description_var = tk.BooleanVar(value=True)
    search_description_check = ttk.Checkbutton(
        options_frame,
        text="Search in Descriptions",
        variable=search_description_var,
        bootstyle="round-toggle"
    )
    search_description_check.grid(row=2, column=0, sticky="w", padx=5, pady=2)

    # Case sensitivity option
    case_sensitive_var = tk.BooleanVar(value=False)
    case_sensitive_check = ttk.Checkbutton(
        options_frame,
        text="Case Sensitive",
        variable=case_sensitive_var,
        bootstyle="round-toggle"
    )
    case_sensitive_check.grid(row=0, column=1, sticky="w", padx=5, pady=2)

    # Match whole word option
    whole_word_var = tk.BooleanVar(value=False)
    whole_word_check = ttk.Checkbutton(
        options_frame,
        text="Match Whole Word",
        variable=whole_word_var,
        bootstyle="round-toggle"
    )
    whole_word_check.grid(row=1, column=1, sticky="w", padx=5, pady=2)

    # Auto-embed after replace option
    auto_embed_var = tk.BooleanVar(value=False)
    auto_embed_check = ttk.Checkbutton(
        options_frame,
        text="Auto-Embed After Replace",
        variable=auto_embed_var,
        bootstyle="round-toggle"
    )
    auto_embed_check.grid(row=2, column=1, sticky="w", padx=5, pady=2)

    # Preview frame
    preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding="10")
    preview_frame.pack(fill="both", expand=True, pady=(0, 10))

    # Create a treeview to show preview of changes
    preview_tree = tkttk.Treeview(
        preview_frame,
        columns=("Field", "Original", "New"),
        show="headings",
        height=8
    )
    preview_tree.heading("Field", text="Field")
    preview_tree.heading("Original", text="Original")
    preview_tree.heading("New", text="New")

    preview_tree.column("Field", width=80)
    preview_tree.column("Original", width=150)
    preview_tree.column("New", width=150)

    preview_tree.pack(fill="both", expand=True)

    # Add scrollbar to preview tree
    preview_scrollbar = ttk.Scrollbar(preview_tree, orient="vertical", command=preview_tree.yview)
    preview_tree.configure(yscrollcommand=preview_scrollbar.set)
    preview_scrollbar.pack(side="right", fill="y")

    # Status label
    status_var = tk.StringVar(value="Ready to search and replace")
    status_label = ttk.Label(main_frame, textvariable=status_var)
    status_label.pack(fill="x", pady=(0, 10))

    # Function to preview changes
    def preview_changes():
        # Clear previous preview
        for item in preview_tree.get_children():
            preview_tree.delete(item)

        find_text = find_entry.get()
        replace_text = replace_entry.get()

        if not find_text:
            status_var.set("Please enter text to find")
            return

        # Get search options
        case_sensitive = case_sensitive_var.get()
        whole_word = whole_word_var.get()

        # Prepare for whole word search if needed
        if whole_word:
            find_pattern = r'\b' + re.escape(find_text) + r'\b'
        else:
            find_pattern = re.escape(find_text)

        # Set flags for case sensitivity
        flags = 0 if case_sensitive else re.IGNORECASE

        # Count replacements
        total_replacements = 0
        affected_items = 0

        # Check each item in the main treeview
        for item_tree in tree.get_children(): # Renamed loop variable
            values = tree.item(item_tree, "values")
            if not values or len(values) < 6: continue # Expect 6 values
            filename, title, keywords, description, category_val, rating_val = values # Unpack 6


            # Check if any replacements would be made
            item_affected = False

            # Check title if selected
            if search_title_var.get() and title:
                new_title = re.sub(find_pattern, replace_text, title, flags=flags)
                if new_title != title:
                    preview_tree.insert("", "end", values=(f"Title ({filename})", title, new_title))
                    total_replacements += len(re.findall(find_pattern, title, flags=flags))
                    item_affected = True

            # Check keywords if selected
            if search_keywords_var.get() and keywords:
                new_keywords = re.sub(find_pattern, replace_text, keywords, flags=flags)
                if new_keywords != keywords:
                    preview_tree.insert("", "end", values=(f"Keywords ({filename})", keywords, new_keywords))
                    total_replacements += len(re.findall(find_pattern, keywords, flags=flags))
                    item_affected = True

            # Check description if selected
            if search_description_var.get() and description:
                new_description = re.sub(find_pattern, replace_text, description, flags=flags)
                if new_description != description:
                    preview_tree.insert("", "end", values=(f"Description ({filename})", description, new_description))
                    total_replacements += len(re.findall(find_pattern, description, flags=flags))
                    item_affected = True

            if item_affected:
                affected_items += 1

        if total_replacements > 0:
            status_var.set(f"Found {total_replacements} occurrences in {affected_items} items")
        else:
            status_var.set("No matches found")

    # Function to apply changes
    def apply_changes():
        find_text = find_entry.get()
        replace_text = replace_entry.get()

        if not find_text:
            status_var.set("Please enter text to find")
            return

        # Get search options
        case_sensitive = case_sensitive_var.get()
        whole_word = whole_word_var.get()

        # Prepare for whole word search if needed
        if whole_word:
            find_pattern = r'\b' + re.escape(find_text) + r'\b'
        else:
            find_pattern = re.escape(find_text)

        # Set flags for case sensitivity
        flags = 0 if case_sensitive else re.IGNORECASE

        # Count replacements
        total_replacements = 0
        affected_items = 0

        # Check each item in the main treeview
        for item_tree in tree.get_children(): # Renamed loop variable
            values = tree.item(item_tree, "values")
            if not values or len(values) < 6: continue # Expect 6 values
            filename, title, keywords, description, category_val, rating_val = values # Unpack 6


            # Track if this item was modified
            item_modified = False

            # Replace in title if selected
            if search_title_var.get() and title:
                new_title = re.sub(find_pattern, replace_text, title, flags=flags)
                if new_title != title:
                    title = new_title
                    item_modified = True
                    total_replacements += 1 # This counts items modified, not individual replacements in text

            # Replace in keywords if selected
            if search_keywords_var.get() and keywords:
                new_keywords = re.sub(find_pattern, replace_text, keywords, flags=flags)
                if new_keywords != keywords:
                    keywords = new_keywords
                    item_modified = True
                    total_replacements += 1 # This counts items modified

            # Replace in description if selected
            if search_description_var.get() and description:
                new_description = re.sub(find_pattern, replace_text, description, flags=flags)
                if new_description != description:
                    description = new_description
                    item_modified = True
                    total_replacements += 1 # This counts items modified

            # Update the item if modified
            if item_modified:
                # Preserve existing rating if available, otherwise use default
                current_values = tree.item(item_tree)["values"]
                rating = current_values[5] if len(current_values) > 5 else "★★★★★"
                tree.item(item_tree, values=(filename, title, keywords, description, category_val, rating)) # Pass category and rating back
                affected_items += 1

                # Auto-embed if option is selected
                if auto_embed_var.get():
                    # Find the original file path
                    file_path = None
                    for i in range(image_listbox.size()):
                        if os.path.basename(image_listbox.get(i)) == filename:
                            file_path = image_listbox.get(i)
                            break

                    if file_path and os.path.exists(file_path):
                        # Call auto_embed_metadata which now includes renaming if auto_embed_enabled is on
                        auto_embed_metadata(file_path, title, keywords, description, called_from_find_replace_with_embed_flag=auto_embed_var.get())


        if total_replacements > 0:
            status_var.set(f"Replaced {total_replacements} occurrences in {affected_items} items")
            messagebox.showinfo("Success", f"Replaced {total_replacements} occurrences in {affected_items} items")
            find_replace_window.destroy()
        else:
            status_var.set("No replacements made")

    # Buttons frame
    buttons_frame = ttk.Frame(main_frame)
    buttons_frame.pack(fill="x")

    preview_button = ttk.Button(
        buttons_frame,
        text="Preview Changes",
        command=preview_changes,
        bootstyle=PRIMARY
    )
    preview_button.pack(side="left", padx=(0, 5))

    apply_button = ttk.Button(
        buttons_frame,
        text="Apply Changes",
        command=apply_changes,
        bootstyle=SUCCESS
    )
    apply_button.pack(side="left", padx=(0, 5))

    cancel_button = ttk.Button(
        buttons_frame,
        text="Cancel",
        command=find_replace_window.destroy,
        bootstyle=DANGER
    )
    cancel_button.pack(side="right")

    # Set focus to find entry
    find_entry.focus_set()

    # Make the window modal
    find_replace_window.transient(root)
    find_replace_window.grab_set()
    root.wait_window(find_replace_window)

def rename_files_based_on_titles():
    """Rename files based on the generated titles and update filenames in both treeview and image_listbox."""
    if not tree.get_children():
        messagebox.showerror("Error", "No data available to rename files.")
        return

    directory = filedialog.askdirectory()
    if not directory:
        return

    renamed_files_map = {}  # Dictionary to track renamed files: original_filename -> new_filename_with_ext

    for item_tree in tree.get_children(): # Renamed loop variable
        values = tree.item(item_tree, "values")
        if not values or len(values) < 2:  # Need at least filename and title
            continue
        original_filename = values[0]
        title = values[1]

        original_filepath = os.path.join(directory, original_filename)
        if not os.path.exists(original_filepath):
            print(f"File not found (skipping rename): {original_filepath}")
            continue

        _, file_extension = os.path.splitext(original_filename)
        new_filename_base = generate_safe_filename_from_title(title)
        
        new_filename_with_ext = f"{new_filename_base}{file_extension}"
        new_filepath = os.path.join(directory, new_filename_with_ext)

        # Handle potential filename collisions
        counter = 1
        temp_new_filepath = new_filepath
        temp_new_filename_with_ext = new_filename_with_ext
        while os.path.exists(temp_new_filepath) and temp_new_filepath != original_filepath:
            temp_new_filename_with_ext = f"{new_filename_base}_{counter}{file_extension}"
            temp_new_filepath = os.path.join(directory, temp_new_filename_with_ext)
            counter += 1
        new_filepath = temp_new_filepath
        new_filename_with_ext = temp_new_filename_with_ext


        if original_filepath != new_filepath:
            try:
                os.rename(original_filepath, new_filepath)
                renamed_files_map[original_filename] = new_filename_with_ext
                print(f"Renamed: {original_filepath} -> {new_filepath}")
            except Exception as e:
                print(f"Error renaming {original_filepath}: {e}")
        else:
            # If new name is same as old, still add to map for UI update consistency if needed
            renamed_files_map[original_filename] = original_filename


    # Update filenames in the treeview
    for item_tree in tree.get_children(): # Renamed loop variable
        values = tree.item(item_tree, "values")
        if not values or len(values) < 1:
            continue
        original_filename = values[0]
        if original_filename in renamed_files_map:
            new_values = list(values)
            new_values[0] = renamed_files_map[original_filename]
            tree.item(item_tree, values=tuple(new_values))

    # Update filenames in the image_listbox
    for i in range(image_listbox.size()):
        original_path = image_listbox.get(i)
        original_filename_in_listbox = os.path.basename(original_path)
        if original_filename_in_listbox in renamed_files_map:
            # Construct new path based on the *selected directory* for renaming, not original_path's dir
            new_path = os.path.join(directory, renamed_files_map[original_filename_in_listbox])
            image_listbox.delete(i)
            image_listbox.insert(i, new_path)


    messagebox.showinfo("Success", "Files renamed based on generated titles and all views updated.")

rename_button = ttk.Button(file_utils_frame, text="Rename Files", command=rename_files_based_on_titles, bootstyle="info-outline", width=12)
rename_button.pack(side=tk.LEFT, padx=3, pady=3)
find_replace_button = ttk.Button(file_utils_frame, text="Find & Replace", command=find_replace_metadata, bootstyle="info-outline", width=14)
find_replace_button.pack(side=tk.LEFT, padx=3, pady=3)

# --- Functions for cleaning filenames (replacing dashes) ---
def custom_replace_dashes(name):
    # Replace triple, double, and single dashes with appropriate spaces
    name = name.replace('---', '   ')
    name = name.replace('--', '  ')
    name = name.replace('-', ' ')
    return name

def rename_files_in_folder_by_dashes(folder_path):
    renamed_count = 0
    error_count = 0
    for filename in os.listdir(folder_path):
        full_path = os.path.join(folder_path, filename)
        if os.path.isfile(full_path):
            name, ext = os.path.splitext(filename)
            new_name_base = custom_replace_dashes(name)
            
            # Avoid creating names that are just spaces or empty
            if not new_name_base.strip():
                print(f"Skipping rename for '{filename}' as new name would be empty or only spaces.")
                continue

            new_name = new_name_base + ext
            new_path = os.path.join(folder_path, new_name)

            if filename != new_name:
                try:
                    # Handle potential filename collisions if new_name already exists
                    counter = 1
                    temp_new_path = new_path
                    temp_new_name = new_name
                    while os.path.exists(temp_new_path) and temp_new_path != full_path:
                        temp_new_name = f"{new_name_base}_{counter}{ext}"
                        temp_new_path = os.path.join(folder_path, temp_new_name)
                        counter += 1
                    new_path = temp_new_path
                    new_name = temp_new_name
                    
                    if filename != new_name: # Check again if collision handling changed the name
                        os.rename(full_path, new_path)
                        print(f"Renamed (dashes): {filename} → {new_name}")
                        renamed_count +=1
                    else:
                        print(f"Skipped (dashes - name unchanged after collision check): {filename}")


                except Exception as e:
                    print(f"Error renaming (dashes) {filename}: {e}")
                    error_count += 1
    if renamed_count > 0 or error_count > 0:
        messagebox.showinfo("Renaming Complete", f"Finished cleaning filenames in folder.\nRenamed: {renamed_count}\nErrors: {error_count}", parent=root)
    else:
        messagebox.showinfo("Renaming Info", "No files needed renaming or no files found.", parent=root)


def select_folder_and_rename_files_gui():
    folder_path = filedialog.askdirectory(title="Select Folder to Clean Filenames (Dashes)", parent=root)

    if folder_path:
        print(f"Selected folder for dash replacement: {folder_path}")
        try:
            rename_files_in_folder_by_dashes(folder_path)
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred during renaming:\n{str(e)}", parent=root)
            print(f"Error during dash replacement process: {e}")
    else:
        print("No folder selected for dash replacement.")

# Add button for cleaning filenames (dashes)
clean_filenames_button = ttk.Button(file_utils_frame, text="Clean Filenames", command=select_folder_and_rename_files_gui, bootstyle="warning-outline", width=15)
clean_filenames_button.pack(side=tk.LEFT, padx=3, pady=3)

def get_exiftool_path():
    """Get the full path to exiftool.exe and set up environment variables."""
    if getattr(sys, 'frozen', False):
        exe_path = os.path.join(sys._MEIPASS, "exiftool.exe")
        os.environ["EXIFTOOL_HOME"] = os.path.join(sys._MEIPASS, "exiftool_files")
        return exe_path
    else:
        exe_path = os.path.abspath("exiftool.exe")
        os.environ["EXIFTOOL_HOME"] = os.path.abspath("exiftool_files")
        return exe_path

def embed_metadata(file_path, title, description, keyword_list, rating="★★★★★"):
    """Embed metadata into a file using ExifTool without showing console window."""
    try:
        print(f"🔧 Starting metadata embedding for: {os.path.basename(file_path)}")

        # Validate inputs
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return False

        if not title or not title.strip():
            print(f"❌ No title provided for embedding")
            return False

        # Clean up keyword list - ensure it's a list and remove empty entries
        keywords = [k.strip() for k in keyword_list if k.strip()]
        print(f"📋 Embedding {len(keywords)} keywords")

        # Convert star rating to numeric value (1-5)
        rating_numeric = rating.count("★") if rating else 5
        print(f"⭐ Embedding rating: {rating} ({rating_numeric}/5)")

        # Detect file type
        file_ext = os.path.splitext(file_path)[1].lower()
        is_vector_file = file_ext in ['.eps', '.ai', '.svg']
        is_writable_vector = file_ext in ['.eps', '.ai']  # SVG is read-only in ExifTool

        print(f"📄 File type: {file_ext}, Vector file: {is_vector_file}, Writable: {is_writable_vector or not is_vector_file}")

        # Check if SVG file (not writable with ExifTool)
        if file_ext == '.svg':
            print(f"⚠️ SVG files are read-only in ExifTool. Metadata embedding not supported for: {os.path.basename(file_path)}")
            return False

        # Check if it's a video file
        video_extensions = {'.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.asf', '.m2v', '.mpg', '.mpeg'}
        is_video = file_ext in video_extensions

        # Base command with common tags
        command = [
            get_exiftool_path(),
            '-overwrite_original',
            '-preserve',
            '-codedcharacterset=utf8',
            '-charset', 'iptc=utf8',
            '-charset', 'exif=utf8',
            f'-XMP-dc:Title={title}',
            f'-XMP-dc:Description={description}',
            f'-XMP-xmp:Rating={rating_numeric}',  # Add rating to XMP
        ]

        # Add keywords using multiple tag types for maximum compatibility
        for keyword in keywords:
            if is_vector_file:
                # For vector files, focus on XMP tags which are more widely supported
                command.extend([
                    f'-XMP-dc:subject+={keyword}',     # XMP Dublin Core subject
                    f'-XMP-dc:creator+={keyword}',     # XMP Dublin Core creator
                    f'-keywords+={keyword}',           # Generic keywords
                ])
            else:
                # For raster images, use full tag set
                command.extend([
                    f'-keywords+={keyword}',           # Append to IPTC keyword
                    f'-subject+={keyword}',            # Append to XMP subject
                    f'-XMP-dc:subject+={keyword}',     # Append to XMP Dublin Core subject
                    f'-IPTC:Keywords+={keyword}'       # Append to IPTC IIM keywords
                ])

        # Add file-type specific tags
        if is_video:
            # Video-specific metadata tags using QuickTime and XMP standards
            print(f"🎬 Adding video-specific metadata tags for: {file_ext}")
            command.extend([
                # QuickTime metadata (for MP4, MOV, M4V)
                f'-QuickTime:Title={title}',
                f'-QuickTime:Description={description}',
                f'-QuickTime:Comment={description}',
                f'-QuickTime:Copyright="All rights reserved"',

                # XMP metadata (widely supported)
                f'-XMP-dc:Title={title}',
                f'-XMP-dc:Description={description}',
                f'-XMP-dc:Rights="All rights reserved"',
                f'-XMP-xmpRights:Marked=True',

                # Additional video metadata
                f'-XMP-photoshop:Headline={title}',
                f'-XMP-photoshop:Instructions={description}',

                # User data for broader compatibility
                f'-UserData:Title={title}',
                f'-UserData:Description={description}',
            ])

            # Add keywords to video metadata
            if keyword_list:
                # Add keywords as XMP subject array
                for keyword in keyword_list:
                    command.append(f'-XMP-dc:Subject+={keyword}')

                # Also add as comma-separated string for compatibility
                keywords_string = ", ".join(keyword_list)
                command.extend([
                    f'-QuickTime:Keywords={keywords_string}',
                    f'-UserData:Keywords={keywords_string}',
                ])

        elif file_path.lower().endswith('.png'):
            # For PNG files, add PNG-specific tags
            command.extend([
                f'-PNG:Title={title}',
                f'-PNG:Description={description}',
                f'-PNG:Author="All rights reserved"',
                f'-PNG:Copyright="All rights reserved"'
            ])
        elif is_vector_file:
            # For vector files (EPS, AI, SVG), add vector-specific XMP tags
            command.extend([
                f'-XMP-dc:creator="All rights reserved"',
                f'-XMP-dc:rights="All rights reserved"',
                f'-XMP-dc:publisher="Meta Master"',
                f'-XMP-xmp:CreatorTool="Meta Master"',
            ])

            # Add EPS/AI specific tags if supported
            if file_ext in ['.eps', '.ai']:
                command.extend([
                    f'-EPS:Title={title}',
                    f'-EPS:Creator="All rights reserved"',
                ])
        else:
            # For JPG and other formats, add standard IPTC/EXIF tags
            command.extend([
                f'-IPTC:ObjectName={title}',
                f'-IPTC:Caption-Abstract={description}',
                f'-EXIF:ImageDescription={description}',
                f'-IPTC:CopyrightNotice="All rights reserved"',
                f'-EXIF:Copyright="All rights reserved"',
                f'-XMP-dc:Rights="All rights reserved"'
            ])

        # Add the file path at the end
        command.append(file_path)

        # Use subprocess with hidden console
        startupinfo = None
        if os.name == 'nt':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

        # Run ExifTool with hidden console
        print(f"🔧 Running ExifTool command...")
        process = subprocess.run(
            command,
            check=True,
            startupinfo=startupinfo,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
            text=True,
            encoding='utf-8',
            timeout=30  # Add timeout to prevent hanging
        )

        print(f"✅ ExifTool execution successful")


        # Verify metadata was written
        verify_command = [
            get_exiftool_path(),
            '-s',
            '-G',
            '-keywords',
            '-subject',
            file_path
        ]

        verify_process = subprocess.run(
            verify_command,
            check=True,
            startupinfo=startupinfo,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
            text=True,
            encoding='utf-8' # Added encoding
        )


        if verify_process.stdout:
            print("Keywords verification:")
            print(verify_process.stdout)

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ ExifTool error: {e}")
        print(f"   Return code: {e.returncode}")
        if e.stderr:
            print(f"   Error details: {e.stderr}")
        if e.stdout:
            print(f"   Output: {e.stdout}")
        return False
    except subprocess.TimeoutExpired:
        print(f"❌ ExifTool timeout: Command took longer than 30 seconds")
        return False
    except Exception as e:
        print(f"❌ Unexpected error in embed_metadata: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def auto_embed_metadata(file_path, title, keywords, description, called_from_find_replace_with_embed_flag=False):
    """Automatically embed metadata into a file after generation and rename if enabled."""

    print(f"🔧 Auto-embed called for: {os.path.basename(file_path)}")
    print(f"   Mode: {mode_var.get()}")
    print(f"   Auto Embed Enabled: {auto_embed_enabled.get()}")
    print(f"   Called from Find/Replace: {called_from_find_replace_with_embed_flag}")

    # Skip if not in Image mode (unless called from find_replace with its own embed flag)
    # Allow Vector mode for EPS/AI files that support metadata embedding
    supported_modes = ["Image", "Vector"]
    if mode_var.get() not in supported_modes and not called_from_find_replace_with_embed_flag:
        print(f"Auto-embed/rename is for Image/Vector modes or specific calls. Current mode: {mode_var.get()}")
        return False

    if file_path.lower().endswith('.png') and png_refine_transparent_enabled.get():
        print(f"Refining PNG background for: {os.path.basename(file_path)} before embedding.")
        refine_png_background(file_path) # This function will modify file_path in place

    # Validate inputs
    if not title or not title.strip():
        print(f"❌ Skipping auto-embed: No title provided for {os.path.basename(file_path)}")
        return False

    if "Error: Rate Limit (429)" in title or "Error:" in title:
        print(f"❌ Skipping auto-embed for file with error in title: {os.path.basename(file_path)}")
        return False

    if not os.path.exists(file_path):
        print(f"❌ File not found for auto-embed: {file_path}")
        return False

    # Check if file is supported for metadata embedding
    # Note: SVG is read-only in ExifTool, so it's excluded from writable formats
    supported_extensions = ['.jpg', '.jpeg', '.png', '.tiff', '.tif', '.eps', '.ai']
    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext not in supported_extensions:
        if file_ext == '.svg':
            print(f"⚠️ SVG files are read-only in ExifTool. Use Adobe Illustrator embedding instead: {os.path.basename(file_path)}")
        else:
            print(f"⚠️ File type {file_ext} not supported for metadata embedding: {os.path.basename(file_path)}")
        return False

    # Description is now handled by process_single_image or passed directly.
    # This function primarily focuses on embedding.
    current_description = description # Use the description passed as a parameter
    if not description_enabled.get(): # Ensure description is blank if not enabled for embedding
        current_description = ""
    # current_description should already be the final intended description from the tree.

    # Clean black background phrases from current_description if refine PNG BG is on (for auto_embed_metadata)
    if file_path.lower().endswith('.png') and png_refine_transparent_enabled.get() and current_description:
        black_background_phrases_to_remove = [
            "Black Background",
            "isolated on a Black Background",
            "Against Black Background",
            "Isolated Against a Black Background"
        ]
        for phrase_to_remove in black_background_phrases_to_remove:
            current_description = re.sub(r'(?i)\b' + re.escape(phrase_to_remove) + r'\b', '', current_description).strip()
        
        current_description = re.sub(r'\s\s+', ' ', current_description).strip()
        current_description = clean_metadata_text(current_description)

    keyword_list = [kw.strip() for kw in re.split(r'[;,|]', keywords) if kw.strip()]

    print(f"📝 Embedding metadata:")
    print(f"   Title: {title[:50]}{'...' if len(title) > 50 else ''}")
    print(f"   Keywords: {len(keyword_list)} keywords")
    print(f"   Description: {len(current_description)} characters")

    # Get rating from tree if available
    rating = "★★★★★"  # Default rating
    for item_tree in tree.get_children():
        values = tree.item(item_tree)["values"]
        if values and values[0] == os.path.basename(file_path):
            rating = values[5] if len(values) > 5 else "★★★★★"
            break

    embed_success = embed_metadata(file_path, title, current_description, keyword_list, rating)

    original_filename_for_ui = os.path.basename(file_path) # Filename before potential rename
    original_filepath_for_rename = file_path # Store original full path

    if embed_success:
        print(f"Auto-embedded metadata for: {original_filename_for_ui}")
        
        # --- Auto-rename logic ---
        if auto_embed_enabled.get() or called_from_find_replace_with_embed_flag:
            directory_for_rename = os.path.dirname(original_filepath_for_rename)
            _, file_extension_for_rename = os.path.splitext(original_filename_for_ui)

            new_filename_base = generate_safe_filename_from_title(title)
            
            new_filename_with_ext_final = f"{new_filename_base}{file_extension_for_rename}"
            new_filepath_final = os.path.join(directory_for_rename, new_filename_with_ext_final)
            
            counter = 1
            while os.path.exists(new_filepath_final) and new_filepath_final != original_filepath_for_rename:
                new_filename_with_ext_final = f"{new_filename_base}_{counter}{file_extension_for_rename}"
                new_filepath_final = os.path.join(directory_for_rename, new_filename_with_ext_final)
                counter += 1

            if original_filepath_for_rename != new_filepath_final:
                try:
                    os.rename(original_filepath_for_rename, new_filepath_final)
                    print(f"Auto-renamed: {original_filename_for_ui} -> {new_filename_with_ext_final}")
                    original_filename_for_ui = new_filename_with_ext_final # Update for treeview display
                    
                    # Update image_listbox (stores full paths)
                    for i in range(image_listbox.size()):
                        if image_listbox.get(i) == original_filepath_for_rename:
                            image_listbox.delete(i)
                            image_listbox.insert(i, new_filepath_final)
                            break
                    file_path = new_filepath_final # Update file_path to the new path for subsequent tree update
                except Exception as e_rename:
                    print(f"Error auto-renaming {original_filename_for_ui}: {e_rename}")
        
        # Update treeview with the (potentially new) filename and final description
        item_found_in_tree = False
        # The filename in the tree might be the original one if renaming didn't happen or failed,
        # or it might be the one we are trying to find if it was already updated by a previous rename (e.g. manual rename then find/replace)
        # We need to find the item based on its *current* name in the tree, which is original_filename_for_ui
        # if renaming was successful and this is the first update for this item,
        # or the old name if renaming failed or wasn't attempted.
        # The most robust way is to find the item by its original path's basename before any rename in this call.
        
        filename_to_find_in_tree = os.path.basename(original_filepath_for_rename) # This was the name when process_single_image added it or before find/replace

        for item_id_in_tree in tree.get_children():
            current_tree_values = tree.item(item_id_in_tree, "values")
            if current_tree_values and current_tree_values[0] == filename_to_find_in_tree:
                # Update with the filename that is now on disk (original_filename_for_ui)
                # Also fetch category and rating from the current tree item to preserve them
                current_values = tree.item(item_id_in_tree, "values")
                category_from_tree = current_values[4] if len(current_values) > 4 else "N/A"
                rating_from_tree = current_values[5] if len(current_values) > 5 else "★★★★★"
                tree.item(item_id_in_tree, values=(original_filename_for_ui, title, keywords, current_description, category_from_tree, rating_from_tree))
                item_found_in_tree = True
                break
        if not item_found_in_tree:
             # If not found by original, try by the potentially new name (if rename happened before this function was called by another process)
             for item_id_in_tree in tree.get_children():
                current_tree_values = tree.item(item_id_in_tree, "values")
                if current_tree_values and current_tree_values[0] == original_filename_for_ui:
                    current_values = tree.item(item_id_in_tree, "values")
                    category_from_tree = current_values[4] if len(current_values) > 4 else "N/A"
                    rating_from_tree = current_values[5] if len(current_values) > 5 else "★★★★★"
                    tree.item(item_id_in_tree, values=(original_filename_for_ui, title, keywords, current_description, category_from_tree, rating_from_tree))
                    item_found_in_tree = True
                    break
             if not item_found_in_tree:
                print(f"Warning: Could not find item {filename_to_find_in_tree} or {original_filename_for_ui} in tree to update after embed/rename.")

    else:
        print(f"Failed to auto-embed metadata for: {original_filename_for_ui}")

    return embed_success


def embed_metadata_with_exiftool():
    """Embed metadata into image files using ExifTool."""
    if not tree.get_children():
        messagebox.showerror("Error", "No metadata available to embed.")
        return

    for item_tree in tree.get_children(): # Renamed loop variable
        values = tree.item(item_tree, "values")
        if not values or len(values) < 6: # Expect 6 values now
            continue

        filename, title, keywords, description, category, rating = values # Unpack all 6

        # Skip files with rate limit errors
        if "Error: Rate Limit (429)" in title:
            continue

        # Find the original file path first
        file_path = None
        for i in range(image_listbox.size()):
            if os.path.basename(image_listbox.get(i)) == filename:
                file_path = image_listbox.get(i)
                break

        if not file_path or not os.path.exists(file_path):
            # Preserve existing rating if available
            current_values = tree.item(item_tree)["values"]
            rating = current_values[5] if len(current_values) > 5 else "☆☆☆☆☆"
            tree.item(item_tree, values=(filename, title, keywords, f"Error: File not found", category, rating))
            continue

        # Description from tree is used.
        # If it's "Pending..." or empty and descriptions are enabled, process_single_image should have handled it.
        # This function now assumes 'description' (values[3]) is the correct one to use or "" if disabled.
        if not description_enabled.get():
            description = "" 

        # Split keywords using comma, semicolon, or pipe
        keyword_list = [kw.strip() for kw in re.split(r'[;,|]', keywords) if kw.strip()]

        success = embed_metadata(file_path, title, description, keyword_list, rating)
        if not success:
            tree.item(item_tree, values=(filename, title, keywords, "Error: Failed to embed metadata", category, rating))
        else: # If successful, update description in tree if it was changed
            if description != values[3]: # values[3] is description
                tree.item(item_tree, values=(filename, title, keywords, description, category, rating))


    messagebox.showinfo("✅ Success", "Image Metadata embedded successfully!")

def embed_vector_metadata_only():
    """Embed metadata into vector files without exporting JPG images."""
    if not tree.get_children():
        messagebox.showerror("Error", "No metadata available to embed.")
        return

    try:
        # Initialize Illustrator
        illustrator_app = win32com.client.Dispatch("Illustrator.Application")
        print("🎨 Illustrator application initialized for metadata embedding only")

        # Get EPS version setting
        eps_version = config.get("eps_version", "illustrator10")
        eps_compatibility = 10 if eps_version == "illustrator10" else 24  # 24 for Illustrator 2020

        print(f"📋 Using EPS compatibility: {eps_version} (version {eps_compatibility})")

        processed_count = 0
        failed_count = 0

        for item in tree.get_children():
            values = tree.item(item, "values")
            if not values or len(values) < 4:
                continue

            filename, title, keywords, description = values[:4]

            # Skip if no title or has error
            if not title or not title.strip() or "Error:" in title:
                print(f"⚠️ Skipping {filename}: No valid title")
                failed_count += 1
                continue

            # Find the file path
            file_path = None
            for i in range(image_listbox.size()):
                if os.path.basename(image_listbox.get(i)) == filename:
                    file_path = image_listbox.get(i)
                    break

            if not file_path or not os.path.exists(file_path):
                print(f"❌ File not found: {filename}")
                failed_count += 1
                continue

            # Check if it's a vector file
            if not any(file_path.lower().endswith(ext) for ext in ['.ai', '.eps', '.svg']):
                print(f"⚠️ Not a vector file: {filename}")
                failed_count += 1
                continue

            try:
                print(f"🔧 Processing vector file: {filename}")

                # Check file extension and handle accordingly
                file_extension = os.path.splitext(file_path)[1].lower()

                # Open the document with error handling
                try:
                    doc = illustrator_app.Open(file_path)
                    print(f"📂 Document opened successfully: {filename}")
                except Exception as open_error:
                    print(f"❌ Error opening file {filename}: {open_error}")
                    if file_extension == '.svg':
                        print(f"⚠️ SVG files may require special handling. Trying alternative method...")
                        # For SVG files, try opening with specific options
                        try:
                            doc = illustrator_app.Open(file_path, 1)  # DocumentColorSpace.RGB
                            print(f"📂 SVG document opened with alternative method: {filename}")
                        except Exception as svg_error:
                            print(f"❌ Alternative SVG opening failed for {filename}: {svg_error}")
                            raise open_error
                    else:
                        raise open_error

                # Prepare metadata
                keyword_list = [kw.strip() for kw in re.split(r'[;,|]', keywords) if kw.strip()]

                # Set document info (metadata) with error handling
                try:
                    doc_info = doc.DocumentInfo
                    doc_info.Title = title
                    doc_info.Subject = description if description_enabled.get() else ""
                    doc_info.Keywords = ", ".join(keyword_list)
                    doc_info.Author = "Meta Master"
                    print(f"📝 Metadata set successfully for: {filename}")
                except Exception as metadata_error:
                    print(f"⚠️ Metadata setting error for {filename}: {metadata_error}")
                    # Continue with saving even if metadata setting fails

                # Determine output path
                original_extension = os.path.splitext(file_path)[1].lower()
                if original_extension != '.eps':
                    eps_file_path = os.path.splitext(file_path)[0] + '.eps'
                else:
                    eps_file_path = file_path

                # Save as EPS with selected version
                try:
                    eps_options = win32com.client.Dispatch("Illustrator.EPSSaveOptions")
                    eps_options.Compatibility = eps_compatibility
                    eps_options.EmbedLinkedFiles = True
                    eps_options.EmbedAllFonts = True
                    doc.SaveAs(eps_file_path, eps_options)
                    print(f"💾 File saved successfully as EPS: {os.path.basename(eps_file_path)}")
                except Exception as save_error:
                    print(f"❌ Error saving EPS file {filename}: {save_error}")
                    # Try alternative save method
                    try:
                        doc.Save()
                        print(f"💾 File saved with original format: {filename}")
                    except Exception as alt_save_error:
                        print(f"❌ Alternative save also failed for {filename}: {alt_save_error}")
                        raise save_error

                # Check file size
                if os.path.exists(eps_file_path):
                    file_size_bytes = os.path.getsize(eps_file_path)
                    file_size_mb = file_size_bytes / (1024 * 1024)
                    print(f"✅ EPS file saved: {file_size_mb:.2f} MB")

                    if file_size_mb < 1.0:
                        print(f"⚠️ Warning: EPS file size is {file_size_mb:.2f} MB - may be too small for microstock sites")
                    else:
                        print(f"✅ Good file size for microstock: {file_size_mb:.2f} MB")

                # Close the document safely
                try:
                    doc.Close(2)  # SaveOptions.DONOTSAVECHANGES = 2
                    print(f"📄 Document closed successfully: {filename}")
                except Exception as close_error:
                    print(f"⚠️ Warning: Error closing document {filename}: {close_error}")
                    # Continue anyway as the file was processed

                processed_count += 1
                print(f"✅ Vector metadata embedded: {filename}")

            except Exception as e:
                print(f"❌ Error processing {filename}: {e}")
                failed_count += 1
                # Ensure document is closed even on error
                try:
                    if 'doc' in locals() and doc:
                        doc.Close(2)
                        print(f"📄 Document closed after error: {filename}")
                except Exception as cleanup_error:
                    print(f"⚠️ Warning: Could not close document after error {filename}: {cleanup_error}")
                    pass

        # Show results
        if processed_count > 0:
            message = f"Successfully embedded metadata into {processed_count} vector file(s)"
            if failed_count > 0:
                message += f"\n{failed_count} file(s) failed or were skipped"
            messagebox.showinfo("✅ Vector Embed Complete", message)
        else:
            messagebox.showerror("❌ Vector Embed Failed", f"No files were processed successfully.\n{failed_count} file(s) failed or were skipped.")

    except Exception as e:
        messagebox.showerror("❌ Illustrator Error", f"Failed to initialize Illustrator or process files: {e}")

def embed_metadata_with_illustrator():
    """Export JPG first, then embed metadata and save ALL vector files as EPS format using any installed Illustrator version."""
    if not tree.get_children():
        messagebox.showerror("Error", "No metadata available to embed.")
        return

    # Check if user wants to embed without export
    vector_embed_without_export = config.get("vector_embed_without_export", False)
    if vector_embed_without_export:
        print("🔧 Vector embed without export enabled - using metadata-only embedding")
        embed_vector_metadata_only()
        return

    try:
        pythoncom.CoInitialize()

        try:
            illustrator_app = win32com.client.Dispatch("Illustrator.Application")
        except Exception:
            messagebox.showerror("Error", "Illustrator is not installed or COM is not registered.")
            return

        # Get EPS version setting
        eps_version = config.get("eps_version", "illustrator10")
        eps_compatibility = 10 if eps_version == "illustrator10" else 24  # 24 for Illustrator 2020

        print(f"📋 Using EPS compatibility: {eps_version} (version {eps_compatibility})")

        for item_tree in tree.get_children(): # Renamed loop variable
            values = tree.item(item_tree, "values")
            if not values or len(values) < 6: # Expect 6 values now
                continue

            filename, title, keywords, description, category, rating = values # Unpack all 6

            # Find the original full file path first
            file_path = None
            for i in range(image_listbox.size()):
                if os.path.basename(image_listbox.get(i)) == filename:
                    file_path = image_listbox.get(i)
                    break

            if not file_path or not os.path.exists(file_path):
                tree.item(item_tree, values=(filename, title, keywords, f"Error: File not found", category))
                continue

            # Description from tree is used.
            if not description_enabled.get():
                description = ""

            try:
                # Validate file path and extension
                if not file_path.lower().endswith(('.svg', '.eps', '.ai')):
                    raise ValueError(f"Unsupported file format: {file_path}")

                # Create JPG file path by replacing the extension
                # jpg_path = os.path.splitext(file_path)[0] + ".jpg" # Old way

                # New jpg_path logic:
                # Derive JPG name from the original vector file's name, but replace spaces with hyphens
                # as Illustrator seems to do this during export.
                vector_basename_no_ext = os.path.splitext(os.path.basename(file_path))[0]
                # Replace one or more spaces with a single hyphen
                jpg_filename_base_hyphenated = re.sub(r'\s+', '-', vector_basename_no_ext)
                vector_dir = os.path.dirname(file_path)
                jpg_path = os.path.join(vector_dir, jpg_filename_base_hyphenated + ".jpg")
                # Now jpg_path is the path with hyphens if the original vector name had spaces (e.g., "My-File-Name.jpg")

                # First, try to export JPG using a simpler method
                try:
                    # First, convert to PNG using a temporary file
                    temp_png_path = os.path.join(tempfile.gettempdir(), f"temp_{os.path.basename(file_path)}.png")

                    # Handle different vector formats
                    if file_path.lower().endswith('.svg'):
                        # Use cairosvg for SVG files with maximum quality
                        cairosvg.svg2png(url=file_path, write_to=temp_png_path, dpi=600)  # Increased DPI for better quality
                        print(f"\u2705 Converted SVG to PNG with maximum quality: {temp_png_path}")

                        # Convert PNG to JPG using PIL with maximum quality
                        try:
                            with Image.open(temp_png_path) as img:
                                # Convert to RGB if it has transparency
                                if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                                    # Always use white background for transparent images
                                    background = Image.new('RGB', img.size, (255, 255, 255))  # White background

                                    # Use the alpha channel as mask when pasting
                                    alpha_mask = img.split()[3] if img.mode == 'RGBA' else None
                                    background.paste(img, mask=alpha_mask)

                                    # Save with maximum quality (100) and optimize=False for best quality
                                    background.save(jpg_path, 'JPEG', quality=100, optimize=False, subsampling=0)
                                    print(f"\u2705 Transparent image saved with white background: {jpg_path}")
                                else:
                                    # Save with maximum quality (100) and optimize=False for best quality
                                    img.convert('RGB').save(jpg_path, 'JPEG', quality=100, optimize=False, subsampling=0)
                                print(f"\u2705 JPG exported successfully with maximum quality: {jpg_path}")
                        except Exception as pil_error:
                            print(f"\u274c Error converting PNG to JPG: {pil_error}")
                            # If conversion fails, inform the user
                            messagebox.showinfo("JPG Export Failed",
                                              f"Could not export {os.path.basename(file_path)} as JPG. Will continue with metadata embedding.")
                    else:
                        # For EPS/AI files, use Illustrator to export directly to JPG with maximum quality
                        doc = illustrator_app.Open(file_path)

                        try:
                            # Try direct JPG export first
                            jpg_options = win32com.client.Dispatch("Illustrator.ExportOptionsJPEG")
                            jpg_options.QualitySetting = 100  # Maximum quality (1-100)
                            # Note: Not all properties are available in all Illustrator versions
                            # Only set properties that are known to be supported
                            try:
                                jpg_options.AntiAliasing = True  # Enable anti-aliasing for smoother edges
                            except:
                                pass

                            try:
                                jpg_options.ArtBoardClipping = True  # Clip to artboard
                            except:
                                pass

                            try:
                                jpg_options.CompressionMethod = 0  # Baseline (Standard)
                            except:
                                pass

                            # Export the document as JPG
                            doc.Export(jpg_path, 1, jpg_options)  # 1 = ai.ExportType.JPEG
                            print(f"\u2705 JPG exported directly using Illustrator with maximum quality: {jpg_path}")
                        except Exception as jpg_export_error:
                            print(f"\u274c Error with direct JPG export: {jpg_export_error}")

                            # Fallback to PNG export and conversion if direct JPG export fails
                            try:
                                # Export as PNG with maximum quality settings
                                png_options = win32com.client.Dispatch("Illustrator.ExportOptionsPNG24")

                                # Set properties safely with try/except blocks
                                try:
                                    png_options.AntiAliasing = True  # Enable anti-aliasing for smoother edges
                                except:
                                    pass

                                try:
                                    png_options.Transparency = True  # Preserve transparency
                                except:
                                    pass

                                try:
                                    png_options.VerticalScale = 300.0  # Increased scale for better quality
                                except:
                                    pass

                                try:
                                    png_options.HorizontalScale = 300.0  # Increased scale for better quality
                                except:
                                    pass

                                try:
                                    png_options.ArtBoardClipping = True  # Clip to artboard
                                except:
                                    pass

                                try:
                                    png_options.Resolution = 300  # Set high resolution (DPI)
                                except:
                                    pass

                                # Export the document as PNG
                                doc.Export(temp_png_path, 5, png_options)  # 5 = ai.ExportType.PNG24
                                print(f"\u2705 Exported to PNG using Illustrator with maximum quality: {temp_png_path}")

                                # Convert PNG to JPG using PIL with maximum quality
                                with Image.open(temp_png_path) as img:
                                    # Convert to RGB if it has transparency
                                    if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                                        # Always use white background for transparent images
                                        background = Image.new('RGB', img.size, (255, 255, 255))  # White background

                                        # Use the alpha channel as mask when pasting
                                        alpha_mask = img.split()[3] if img.mode == 'RGBA' else None
                                        background.paste(img, mask=alpha_mask)

                                        # Save with maximum quality (100) and optimize=False for best quality
                                        background.save(jpg_path, 'JPEG', quality=100, optimize=False, subsampling=0)
                                        print(f"\u2705 Transparent image saved with white background: {jpg_path}")
                                    else:
                                        # Save with maximum quality (100) and optimize=False for best quality
                                        img.convert('RGB').save(jpg_path, 'JPEG', quality=100, optimize=False, subsampling=0)
                                print(f"\u2705 JPG exported successfully with maximum quality: {jpg_path}")
                            except Exception as fallback_error:
                                print(f"\u274c Fallback PNG-to-JPG conversion also failed: {fallback_error}")
                                messagebox.showinfo("JPG Export Failed",
                                                  f"Could not export {os.path.basename(file_path)} as JPG. Will continue with metadata embedding.")

                        # Close the document
                        doc.Close(2)  # SaveOptions.DONOTSAVECHANGES = 2

                    # Clean up temporary PNG
                    if os.path.exists(temp_png_path):
                        os.remove(temp_png_path)

                    # After JPG is created, embed metadata into it
                    if os.path.exists(jpg_path):
                        keyword_list_for_jpg = [kw.strip() for kw in re.split(r'[;,|]', keywords) if kw.strip()]
                        # Get rating from tree
                        current_values = tree.item(item_tree)["values"]
                        rating = current_values[5] if len(current_values) > 5 else "★★★★★"
                        embed_success_jpg = embed_metadata(jpg_path, title, description, keyword_list_for_jpg, rating)
                        if embed_success_jpg:
                            print(f"\u2705 Successfully embedded metadata into exported JPG: {os.path.basename(jpg_path)}")
                        else:
                            print(f"\u274c Failed to embed metadata into exported JPG: {os.path.basename(jpg_path)}")
                    else:
                        print(f"\u274c Exported JPG not found, cannot embed metadata: {jpg_path}")


                except Exception as jpg_error:
                    print(f"\u274c Error exporting JPG: {jpg_error}")
                    # If JPG export fails, continue with metadata embedding

                # Now open the file again to embed metadata and save as EPS
                doc = illustrator_app.Open(file_path)

                # Validate keywords before building XMP metadata
                if not keywords or not isinstance(keywords, str):
                    raise ValueError("Invalid keywords provided for metadata embedding.")

                # Build XMP metadata string with enhanced description embedding
                xmp_metadata = f"""
                <x:xmpmeta xmlns:x="adobe:ns:meta/">
                    <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
                        <rdf:Description rdf:about=""
                            xmlns:dc="http://purl.org/dc/elements/1.1/"
                            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
                            xmlns:photoshop="http://ns.adobe.com/photoshop/1.0/">
                            <dc:title>{title}</dc:title>
                            <dc:description>{description}</dc:description>
                            <xmp:Description>{description}</xmp:Description>
                            <photoshop:Headline>{title}</photoshop:Headline>
                            <photoshop:Caption>{description}</photoshop:Caption>
                            <dc:subject>
                                <rdf:Bag>
                                    {''.join(f"<rdf:li>{kw.strip()}</rdf:li>" for kw in keywords.split(','))}
                                </rdf:Bag>
                            </dc:subject>
                        </rdf:Description>
                    </rdf:RDF>
                </x:xmpmeta>
                """.strip()
                doc.XMPString = xmp_metadata

                # Generate EPS filename - change extension to .eps if it's not already
                original_extension = os.path.splitext(file_path)[1].lower()
                if original_extension != '.eps':
                    eps_file_path = os.path.splitext(file_path)[0] + '.eps'
                else:
                    eps_file_path = file_path

                # Save as EPS with selected compatibility version
                eps_options = win32com.client.Dispatch("Illustrator.EPSSaveOptions")
                eps_options.Compatibility = eps_compatibility  # Use setting from config
                eps_options.EmbedLinkedFiles = True  # embedLinkedFiles = true
                eps_options.EmbedAllFonts = True  # embedAllFonts = true
                doc.SaveAs(eps_file_path, eps_options)

                # Check file size
                if os.path.exists(eps_file_path):
                    file_size_bytes = os.path.getsize(eps_file_path)
                    file_size_mb = file_size_bytes / (1024 * 1024)
                    print(f"\u2705 EPS file saved: {file_size_mb:.2f} MB")

                    if file_size_mb < 1.0:
                        print(f"\u26A0 Warning: EPS file size is {file_size_mb:.2f} MB - may be too small for microstock sites")
                    else:
                        print(f"\u2705 Good file size for microstock: {file_size_mb:.2f} MB")

                # Log success message
                eps_version_name = "EPS 10" if eps_compatibility == 10 else "EPS 2020"
                if original_extension != '.eps':
                    print(f"\u2705 {original_extension.upper()} file converted and saved as {eps_version_name} with metadata: {os.path.basename(eps_file_path)}")
                else:
                    print(f"\u2705 EPS file saved as {eps_version_name} with metadata: {os.path.basename(eps_file_path)}")

                # Close the document
                doc.Close(2)  # SaveOptions.DONOTSAVECHANGES = 2

                # Update treeview - keep original description, don't show success message
                tree.item(item_tree, values=(filename, title, keywords, description, category))

            except FileNotFoundError as e:
                print(f"\u274c File not found: {e}")
                tree.item(item_tree, values=(filename, title, keywords, f"Error: {e}", category))
            except ValueError as e:
                print(f"\u274c Invalid argument: {e}")
                tree.item(item_tree, values=(filename, title, keywords, f"Error: {e}", category))
            except PermissionError as e:
                print(f"\u274c Permission error: {e}")
                tree.item(item_tree, values=(filename, title, keywords, f"Error: {e}", category))
            except Exception as e:
                # Log detailed Illustrator error
                print(f"\u274c Illustrator error for {file_path}: {e}")
                tree.item(item_tree, values=(filename, title, keywords, f"Error: {e}", category))
            finally:
                # Ensure the document is closed
                try:
                    if 'doc' in locals() and doc:
                        doc.Close(2)  # SaveOptions.DONOTSAVECHANGES = 2
                except Exception:
                    pass

        eps_version_name = "EPS 10" if eps_compatibility == 10 else "EPS 2020"
        messagebox.showinfo("\u2705 Success", f"Maximum quality JPG files exported and vector metadata embedded successfully as {eps_version_name}.")

    except Exception as e:
        messagebox.showerror("\u274c Illustrator Error", f"{e}")

    finally:
        pythoncom.CoUninitialize()

# Add manual embed button for images
def manual_embed_images():
    """Manually embed metadata into selected image and video files."""
    if not tree.get_children():
        messagebox.showerror("Error", "No metadata available to embed.")
        return

    # Get selected items or all items if none selected
    selected_items = tree.selection()
    if not selected_items:
        # If no selection, ask user if they want to embed all
        result = messagebox.askyesno(
            "Embed All Files",
            "No items selected. Do you want to embed metadata into all files (images and videos)?"
        )
        if result:
            selected_items = tree.get_children()
        else:
            return

    embedded_count = 0
    failed_count = 0

    for item in selected_items:
        values = tree.item(item, "values")
        if not values or len(values) < 4:
            continue

        filename, title, keywords, description = values[:4]

        # Skip if no title or has error
        if not title or not title.strip() or "Error:" in title:
            print(f"⚠️ Skipping {filename}: No valid title")
            failed_count += 1
            continue

        # Find the file path
        file_path = None
        for i in range(image_listbox.size()):
            if os.path.basename(image_listbox.get(i)) == filename:
                file_path = image_listbox.get(i)
                break

        if not file_path or not os.path.exists(file_path):
            print(f"❌ File not found: {filename}")
            failed_count += 1
            continue

        # Check if it's a supported format for embedding
        # Note: SVG is read-only in ExifTool, so it's excluded from writable formats
        image_extensions = ['.jpg', '.jpeg', '.png', '.tiff', '.tif', '.eps', '.ai']
        video_extensions = ['.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.asf', '.m2v', '.mpg', '.mpeg']
        supported_extensions = image_extensions + video_extensions

        if not any(file_path.lower().endswith(ext) for ext in supported_extensions):
            if file_path.lower().endswith('.svg'):
                print(f"⚠️ SVG files are read-only in ExifTool. Use Adobe Illustrator embedding instead: {filename}")
            else:
                print(f"⚠️ Unsupported format for embedding: {filename}")
            failed_count += 1
            continue

        # Determine file type for logging
        is_video = any(file_path.lower().endswith(ext) for ext in video_extensions)
        file_type = "video" if is_video else "image"

        # Embed metadata
        file_type_emoji = "🎬" if is_video else "🖼️"
        print(f"🔧 Manual embedding {file_type}: {filename}")
        keyword_list = [kw.strip() for kw in re.split(r'[;,|]', keywords) if kw.strip()]

        # Use description if enabled, otherwise empty
        embed_description = description if description_enabled.get() else ""

        # Get rating from tree
        rating = "★★★★★"  # Default rating
        for item_tree in tree.get_children():
            values = tree.item(item_tree)["values"]
            if values and values[0] == filename:
                rating = values[5] if len(values) > 5 else "★★★★★"
                break

        success = embed_metadata(file_path, title, embed_description, keyword_list, rating)
        if success:
            embedded_count += 1
            print(f"✅ {file_type_emoji} Embedded {file_type}: {filename}")
        else:
            failed_count += 1
            print(f"❌ {file_type_emoji} Failed {file_type}: {filename}")

    # Show results
    if embedded_count > 0:
        message = f"Successfully embedded metadata into {embedded_count} file(s)"
        if failed_count > 0:
            message += f"\n{failed_count} file(s) failed or were skipped"
        messagebox.showinfo("✅ Manual Embed Complete", message)
    else:
        messagebox.showerror("❌ Manual Embed Failed", f"No files were processed successfully.\n{failed_count} file(s) failed or were skipped.")

manual_embed_button = ttk.Button(
    file_utils_frame,
    text="Embed Metadata",
    command=manual_embed_images,
    bootstyle="success-outline",
    width=13
)
manual_embed_button.pack(side=tk.LEFT, padx=3, pady=3)

# Add a button to the UI for exporting JPG and embedding metadata into vector files
illustrator_embed_button = ttk.Button(
    file_utils_frame, # Moved to file_utils_frame
    text="Embed Vector",
    command=embed_metadata_with_illustrator,
    bootstyle="primary-outline", # Changed style
    width=14
)
illustrator_embed_button.pack(side=tk.LEFT, padx=3, pady=3)
# Note: The visibility and state of illustrator_embed_button is handled in toggle_mode

def extract_frame_from_video(video_path, output_image_path, time_seconds=5):
    """Extract a frame from a video at specified time."""
    try:
        cap = cv2.VideoCapture(video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_number = int(fps * time_seconds)
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

        success, frame = cap.read()
        if success:
            cv2.imwrite(output_image_path, frame)
            return output_image_path
        else:
            return None
    finally:
        if 'cap' in locals() and cap.isOpened(): # Check if cap was initialized and is open
            cap.release()


# Modify the extract function to accept a frame count
def extract_n_frames(video_path, count=4):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened(): # Check if video opened successfully
        print(f"Error: Could not open video {video_path}")
        return []

    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total_frames == 0: # Handle videos with no frames
        print(f"Error: Video {video_path} has no frames.")
        cap.release()
        return []

    step = total_frames // (count + 1)
    if step == 0 and count > 0 : # Ensure step is at least 1 if count > 0 and total_frames is small
        step = 1

    extracted_paths = []

    for i in range(1, count + 1):
        frame_no = i * step
        if frame_no >= total_frames and total_frames > 0: # Ensure frame_no is within bounds
            frame_no = total_frames -1 
        
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_no)
        success, frame = cap.read()
        if success:
            out_path = os.path.join(tempfile.gettempdir(), f"frame_{i}_{os.path.basename(video_path)}.jpg") # More unique temp name
            cv2.imwrite(out_path, frame)
            extracted_paths.append(out_path)

    cap.release()
    return extracted_paths

# Update the video processing function to use the selected frame count
def process_video_frames_for_metadata(video_path):
    count = frame_count_var.get()
    if count < 1 or count > 10:
        messagebox.showerror("Invalid Frame Count", "Please choose between 1 and 10 frames.")
        return

    frames = extract_n_frames(video_path, count)

    if not frames:
        messagebox.showerror("Error", "No frames extracted.")
        return

    try:
        api_key = load_gemini_api_key()
        if not api_key:
            messagebox.showerror("Gemini Error", "API key not found. Please enter it in settings.")
            return

        genai.configure(api_key=api_key)
        model = genai.GenerativeModel("gemini-2.5-flash-lite")

        # Optimized prompt with reduced token count
        prompt_parts = [
            f"You are an expert SEO specialist creating high-converting metadata for stock video footage on platforms like Adobe Stock, Shutterstock, etc. Your goal is to maximize discoverability and sales.",
            f"Analyze the following {count} frames from a video. Generate a distinct, highly relevant, and SEO-optimized title, a comprehensive set of keywords, and a compelling descriptive summary.",
            f"Title: Craft a concise, professional, and keyword-rich title ({min_title_words_var.get()}-{max_title_words_var.get()} characters, NO PUNCTUATION) that accurately captures the main subject, action, and key selling points of the video. Ensure the title is unique and engaging for potential buyers. Include primary keywords naturally.",
            f"Keywords: Generate 20-30 diverse and relevant keywords. Focus on long-tail keywords, synonyms, and related concepts. Describe the video's content, style, theme, mood, action, setting, and potential uses. Prioritize keywords that buyers are likely to search for. Ensure variety and avoid keyword stuffing. Include conceptual keywords where appropriate.",
            f"Description: Write a 30-60 word summary that effectively describes the video in detail, highlighting its unique features, action, and benefits for potential buyers. Be specific, engaging, and use descriptive language. Incorporate relevant keywords naturally. DO NOT use generic opening phrases like 'This video shows...' or 'This is footage of...'. Focus on what makes this video stand out and its commercial appeal.",
            "IMPORTANT: The generated metadata (title, keywords, description) should focus purely on the visual content, subject, action, and potential commercial applications of the video. Avoid using generic industry terms like 'video', 'clip', 'footage', 'stock video', unless they are essential for describing a specific style. Emphasize terms that resonate with buyers on microstock sites.",
        ]
        if single_word_keywords_var.get():
            prompt_parts.append("IMPORTANT: All generated keywords MUST be single words. Do not use multi-word phrases as keywords.")


        # Include custom words in the prompt if provided
        custom_words = custom_words_var.get().strip()
        if custom_words:
            prompt_parts.append(f"Include: {custom_words}")

        # Add custom prompt parts if configured
        custom_prompt_parts = config.get("custom_prompt_parts", "").strip()
        if custom_prompt_parts:
            prompt_parts.append(f"Additional instructions: {custom_prompt_parts}")

        for frame_path in frames:
            img = Image.open(frame_path).convert("RGB")
            prompt_parts.append(img)

        # Use optimized model parameters
        response = model.generate_content(
            prompt_parts,
            generation_config={
                "temperature": 0.3,
                "max_output_tokens": 500,
                "top_p": 0.95,
            }
        )
        result = response.text.split("\n")

        # Parse and clean the metadata with negative words filtering
        title = ""
        keywords = ""
        description = ""

        for line in result:
            if line.startswith("Title:"):
                raw_title_from_gemini = line.replace("Title:", "").strip()
                # Insert spaces before capital letters in a PascalCase/camelCase string
                spaced_title = re.sub(r'(?<!^)(?=[A-Z])', ' ', raw_title_from_gemini)
                title = clean_metadata_text(spaced_title, is_title=True)
            elif line.startswith("Keywords:"):
                keywords = clean_metadata_text(line.replace("Keywords:", "").strip())
            elif line.startswith("Description:"):
                description = clean_metadata_text(line.replace("Description:", "").strip())
        
        # Validate title length based on character count settings
        min_title_chars = min_title_words_var.get()
        max_title_chars = max_title_words_var.get()

        # Ensure title meets minimum length by using descriptive terms from keywords
        if len(title) < min_title_chars:
            # Extract descriptive terms from keywords if available
            descriptive_terms = []
            if keywords:
                keyword_list = [kw.strip() for kw in keywords.split(',')[:5]]  # Use first 5 keywords
                # Filter out generic terms and keep specific descriptive ones
                for kw in keyword_list:
                    if (len(kw) > 3 and
                        kw.lower() not in ['professional', 'high', 'quality', 'modern', 'creative', 'design', 'premium', 'elegant', 'video', 'footage', 'clip'] and
                        kw.lower() not in title.lower()):
                        descriptive_terms.append(kw)

            # If no good keywords, use minimal file-type specific terms as last resort
            if not descriptive_terms:
                descriptive_terms = ["video"]

            original_title_words = set(t.lower() for t in title.split())
            temp_title_parts = title.split()

            for term in descriptive_terms[:2]:  # Limit to 2 additional terms
                if len(" ".join(temp_title_parts)) >= min_title_chars: break
                if term.lower() not in original_title_words:
                    potential_new_title = f"{' '.join(temp_title_parts)} {term}".strip()
                    if len(potential_new_title) <= max_title_chars:
                        temp_title_parts.append(term)
                        original_title_words.add(term.lower())
            title = " ".join(temp_title_parts)

        # Ensure title doesn't exceed maximum length
        if len(title) > max_title_chars:
            words = title.split()
            truncated_title = ""
            for word in words:
                if len(truncated_title) + len(word) + (1 if truncated_title else 0) <= max_title_chars -3: 
                    truncated_title += (" " if truncated_title else "") + word
                else:
                    break
            title = truncated_title + "..." if truncated_title else title[:max_title_chars-3] + "..."
        
        title = clean_metadata_text(title, is_title=True) # Final clean

        if single_word_keywords_var.get() and keywords:
            all_single_words = []
            for kw_phrase in keywords.split(','):
                all_single_words.extend(kw_phrase.strip().split())
            keywords = ", ".join(list(OrderedDict.fromkeys(word for word in all_single_words if word)))


        # Update the result with cleaned metadata
        cleaned_result = []
        if title:
            cleaned_result.append(f"Title: {title}")
        if keywords:
            cleaned_result.append(f"Keywords: {keywords}")
        if description:
            cleaned_result.append(f"Description: {description}")

        print("🎬 Video Metadata:\n", "\n".join(cleaned_result))
        messagebox.showinfo("Video Metadata Generated", "\n".join(cleaned_result))

        # Don't reset time displays here - let the process_images function handle it
        status_message_label.config(text="Video processed successfully", foreground="#008000")

    except Exception as e:
        messagebox.showerror("Error", f"Failed to process video frames: {e}")
    finally:
        # Cleanup extracted frames
        for frame_path in frames:
            if os.path.exists(frame_path):
                os.remove(frame_path)

def process_video_frame_for_metadata(image_path, original_video_path, retry_count=0):
    """Process a video frame for metadata generation."""
    try:
        # Find the item in the treeview first to update status
        filename = os.path.basename(original_video_path)
        video_path = original_video_path  # Store for reference in caching
        item_id = None
        for item_tree in tree.get_children(): # Renamed loop variable
            values = tree.item(item_tree)["values"]
            if values and values[0] == filename:
                item_id = item_tree
                # Update status to Processing
                tree.item(item_id, values=(filename, "Processing...", "Processing...", "Processing...", "Processing...", "★★★★★"))
                break

        img = Image.open(image_path).convert("RGB")

        api_key = load_gemini_api_key()
        if not api_key:
            if item_id:
                tree.item(item_id, values=(filename, "Error: No API key", "", "", "Error", "☆☆☆☆☆"))
            messagebox.showerror("Gemini Error", "No API keys found. Please add at least one API key.")
            return False

        try:
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel("gemini-2.5-flash-lite")
        except Exception as e:
            error_message = str(e)

            # Check if this is a rate limit error
            if "429" in error_message and retry_count < 3:  # Limit retries to prevent infinite loops
                # Try switching to another API key
                new_key = switch_to_next_api_key()
                if new_key:
                    # Update status to show we're retrying with a different key
                    if item_id:
                        tree.item(item_id, values=(filename, "Retrying with different API key...", "", "", "Retrying...", "☆☆☆☆☆"))

                    # Update the API key status indicator
                    update_api_key_status()

                    # Retry with the new key
                    return process_video_frame_for_metadata(image_path, original_video_path, retry_count + 1)

            # If not a rate limit error or we've exhausted retries
            if item_id:
                tree.item(item_id, values=(filename, "Error: API Setup Failed", error_message, "", "Error", "☆☆☆☆☆"))
            messagebox.showerror("Gemini Setup Failed", f"API Error: {error_message}")
            return False

        # Check if the image has transparency
        has_transparency = img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info)

        # Optimized prompt with reduced token count and transparency handling
        prompt_parts_list = [ 
            "You are an expert SEO specialist creating high-converting metadata for stock video footage on platforms like Adobe Stock, Shutterstock, etc. Your goal is to maximize discoverability and sales.",
            f"For the provided video frame, generate a distinct, highly relevant, and SEO-optimized title, a comprehensive set of keywords, and a compelling descriptive summary.",
            f"Title: Craft a concise, professional, and keyword-rich title ({min_title_words_var.get()}-{max_title_words_var.get()} words, NO PUNCTUATION) that accurately captures the main subject, action, and key selling points of the video frame. Ensure the title is unique and engaging for potential buyers. Include primary keywords naturally.",
            f"Keywords: Generate {min_keywords_var.get()}-{max_keywords_var.get()} diverse and relevant keywords. Focus on long-tail keywords, synonyms, and related concepts. Describe the video frame's content, style, theme, mood, action, setting, and potential uses. Prioritize keywords that buyers are likely to search for. Ensure variety and avoid keyword stuffing. Include conceptual keywords where appropriate.",
            f"Description: Write a {min_description_var.get()}-{max_description_var.get()} character summary that effectively describes the video frame in detail, highlighting its unique features, action, and benefits for potential buyers. Be specific, engaging, and use descriptive language. Incorporate relevant keywords naturally. DO NOT use generic opening phrases like 'This video shows...' or 'This is footage of...'. Focus on what makes this video frame stand out and its commercial appeal.",
            "IMPORTANT: The generated metadata (title, keywords, description) should focus purely on the visual content, subject, action, and potential commercial applications of the video frame. Avoid using generic industry terms like 'video', 'clip', 'footage', 'stock video', unless they are essential for describing a specific style. Emphasize terms that resonate with buyers on microstock sites.",
        ]
        if single_word_keywords_var.get():
            prompt_parts_list.append("IMPORTANT: All generated keywords MUST be single words. Do not use multi-word phrases as keywords.")


        if has_transparency: # This check might be less relevant for video frames but kept for consistency
            prompt_parts_list.extend([
                "IMPORTANT: If the frame suggests transparency (e.g., isolated subject), describe it as being 'on a white background' or 'with transparent background', NEVER 'on a black background'.",
                "Include keywords related to 'transparent', 'isolated', 'cutout', and 'professional' if applicable to the frame's content.",
            ])
        
        prompt_parts_list.extend([
            "Strictly format your response as:\nTitle: [Generated Title]\nKeywords: [comma-separated keywords]\nDescription: [Generated Description]",
            img,
        ])


        # Include custom words in the prompt if provided
        custom_words = custom_words_var.get().strip()
        if custom_words:
            prompt_parts_list.insert(-1, f"Include: {custom_words}") # Insert before the image

        # Add custom prompt parts if configured
        custom_prompt_parts = config.get("custom_prompt_parts", "").strip()
        if custom_prompt_parts:
            prompt_parts_list.insert(-1, f"Additional instructions: {custom_prompt_parts}")

        # Use optimized model parameters
        response = model.generate_content(
            prompt_parts_list,
            generation_config={
                "temperature": 0.3,
                "max_output_tokens": max_description_var.get() + 100, # Adjusted token limit
                "top_p": 0.95,
            }
        )
        metadata = response.text.split('\n')

        title = ""
        keywords = ""
        description_short = ""  # This is the short description from initial generation

        for line in metadata:
            if line.startswith("Title: "):
                raw_title_from_gemini = line.replace("Title: ", "").strip()
                # Insert spaces before capital letters in a PascalCase/camelCase string
                spaced_title = re.sub(r'(?<!^)(?=[A-Z])', ' ', raw_title_from_gemini)
                title = clean_metadata_text(spaced_title, is_title=True)
            elif line.startswith("Keywords: "):
                keywords = clean_metadata_text(line.replace("Keywords: ", "").strip())
            elif line.startswith("Description: "):
                description_short = clean_metadata_text(line.replace("Description: ", "").strip())

        title = clean_metadata_text(title, is_title=True)
        keywords = clean_metadata_text(keywords, is_title=False)

        # Validate title length based on character count settings
        min_title_chars = min_title_words_var.get()
        max_title_chars = max_title_words_var.get()

        # Ensure title meets minimum length by using descriptive terms from keywords
        if len(title) < min_title_chars:
            # Extract descriptive terms from keywords if available
            descriptive_terms = []
            if keywords:
                keyword_list = [kw.strip() for kw in keywords.split(',')[:5]]  # Use first 5 keywords
                # Filter out generic terms and keep specific descriptive ones
                for kw in keyword_list:
                    if (len(kw) > 3 and
                        kw.lower() not in ['professional', 'high', 'quality', 'modern', 'creative', 'design', 'premium', 'elegant', 'video', 'footage', 'clip'] and
                        kw.lower() not in title.lower()):
                        descriptive_terms.append(kw)

            # If no good keywords, use minimal file-type specific terms as last resort
            if not descriptive_terms:
                descriptive_terms = ["video"]

            original_title_words = set(t.lower() for t in title.split())
            temp_title_parts = title.split()

            for term in descriptive_terms[:2]:  # Limit to 2 additional terms
                if len(" ".join(temp_title_parts)) >= min_title_chars: break
                if term.lower() not in original_title_words:
                    potential_new_title = f"{' '.join(temp_title_parts)} {term}".strip()
                    if len(potential_new_title) <= max_title_chars:
                        temp_title_parts.append(term)
                        original_title_words.add(term.lower())
            title = " ".join(temp_title_parts)

        # Ensure title doesn't exceed maximum length
        if len(title) > max_title_chars:
            words = title.split()
            truncated_title = ""
            for word in words:
                if len(truncated_title) + len(word) + (1 if truncated_title else 0) <= max_title_chars -3: 
                    truncated_title += (" " if truncated_title else "") + word
                else:
                    break
            title = truncated_title + "..." if truncated_title else title[:max_title_chars-3] + "..."
        
        # Add custom title words and keywords
        title = add_custom_title_words(title, max_title_chars)
        keywords = add_custom_keywords(keywords)

        title = clean_metadata_text(title, is_title=True) # Final clean

        if single_word_keywords_var.get() and keywords:
            all_single_words = []
            for kw_phrase in keywords.split(','):
                all_single_words.extend(kw_phrase.strip().split())
            keywords = ", ".join(list(OrderedDict.fromkeys(word for word in all_single_words if word)))


        description = description_short # Start with the initially generated short description
        if not description: # Fallback if Gemini didn't provide one
            description = f"Professional stock video footage featuring {title.lower()} for {keywords.split(',')[0].strip()} projects."
        
        description = clean_metadata_text(description)

        # Ensure description is within the specified character count range
        min_chars_desc = min_description_var.get()
        max_chars_desc = max_description_var.get()
        char_count_desc = len(description)

        if char_count_desc > max_chars_desc:
            # Truncate to max_chars_desc, trying to preserve whole words
            if description[max_chars_desc-3:max_chars_desc] == "...":
                 description = description[:max_chars_desc]
            else:
                last_space = description.rfind(' ', 0, max_chars_desc - 3)
                if last_space != -1 and max_chars_desc - last_space < 20:
                    description = description[:last_space] + "..."
                else:
                    description = description[:max_chars_desc-3] + "..."
        elif char_count_desc < min_chars_desc:
            extension = f" Ideal for various creative and commercial video projects."
            if char_count_desc + len(extension) <= max_chars_desc:
                description += extension
            else: 
                remaining_space = max_chars_desc - char_count_desc - 3 
                if remaining_space > 0:
                    description += extension[:remaining_space] + "..."
                elif char_count_desc > 0 : 
                    description = description[:max_chars_desc-3] + "..." if char_count_desc > max_chars_desc -3 else description


        # Update existing item or insert new one with all metadata including description
        if item_id:
            tree.item(item_id, values=(filename, title, keywords, description, "Video", "★★★★★")) # Default category for video
        else:
            tree.insert("", "end", values=(filename, title, keywords, description, "Video", "★★★★★")) # Default category for video

        # Auto embed metadata if enabled, otherwise show manual embed message
        if auto_embed_enabled.get():
            print(f"🔧 Scheduling auto embed for video: {filename}")
            root.after(100, lambda file=original_video_path, t=title, k=keywords, d=description:
                       auto_embed_metadata(file, t, k, d))
        else:
            print(f"📝 Video metadata generated for: {filename} - Use 'Embed Metadata' button to embed manually")

        return True

    except Exception as e:
        error_message = str(e)
        error_type = "Error"

        # Check if it's a rate limit error (429)
        if "429" in error_message and retry_count < 3:  # Limit retries to prevent infinite loops
            # Try switching to another API key
            new_key = switch_to_next_api_key()
            if new_key:
                # Update status to show we're retrying with a different key
                if item_id:
                    tree.item(item_id, values=(filename, "Retrying with different API key...", "", "", "Retrying...", "☆☆☆☆☆"))

                # Update the API key status indicator
                update_api_key_status()

                # Retry with the new key
                return process_video_frame_for_metadata(image_path, original_video_path, retry_count + 1)
            else:
                # No alternative keys available
                error_type = "Error: Rate Limit (429)"
                # Don't attempt to auto-embed metadata for rate-limited files
                auto_embed_enabled.set(False)
                # Schedule to re-enable auto-embed after this file
                root.after(500, lambda: auto_embed_enabled.set(True))

        if item_id:
            tree.item(item_id, values=(filename, error_type, error_message, "", "Error", "☆☆☆☆☆"))
        else:
            tree.insert("", "end", values=(filename, error_type, error_message, "", "Error", "☆☆☆☆☆"))

        messagebox.showerror("Error", f"Failed to process video frame: {e}")
        return False

# Create a single frame to hold all metadata
combined_frame = ttk.LabelFrame(main_frame, text="Files and Metadata")
combined_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

# Use a single column layout
combined_frame.columnconfigure(0, weight=1)
combined_frame.rowconfigure(1, weight=1)

# Row 0: Label
files_label = ttk.Label(combined_frame, text="Files and Generated Metadata")
files_label.grid(row=0, column=0, sticky="w", padx=5, pady=(5, 0))

# Row 1: Treeview with scrollbars
treeview_scroll_frame = ttk.Frame(combined_frame)
treeview_scroll_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
treeview_scroll_frame.columnconfigure(0, weight=1)
treeview_scroll_frame.rowconfigure(0, weight=1)

# Create treeview with filename, title, keywords, description, category and rating columns
tree = tkttk.Treeview(treeview_scroll_frame, columns=("Filename", "Title", "Keywords", "Description", "Category", "Rating"), show="headings")
tree.heading("Filename", text="Filename")
tree.heading("Title", text="Title")
tree.heading("Keywords", text="Keywords")
tree.heading("Description", text="Description")
tree.heading("Category", text="Category")
tree.heading("Rating", text="Rating")


# Set column widths
tree.column("Filename", width=150)
tree.column("Title", width=200)
tree.column("Keywords", width=230) # Slightly reduced to make room for rating
tree.column("Description", width=230) # Slightly reduced to make room for rating
tree.column("Category", width=100)
tree.column("Rating", width=80)

tree.grid(row=0, column=0, sticky="nsew")

# Add vertical scrollbar for the treeview
v_scrollbar_tree = ttk.Scrollbar(treeview_scroll_frame, orient=tk.VERTICAL, command=tree.yview)
v_scrollbar_tree.grid(row=0, column=1, sticky="ns")

# Add horizontal scrollbar for the treeview
h_scrollbar = ttk.Scrollbar(treeview_scroll_frame, orient=tk.HORIZONTAL, command=tree.xview)
h_scrollbar.grid(row=1, column=0, sticky="ew")

# Configure the treeview to use both scrollbars
tree.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar_tree.set)

# Create a hidden listbox to store file paths (not visible in UI)
image_listbox = tk.Listbox(combined_frame, selectmode=tk.MULTIPLE)
image_listbox.grid_forget()  # Hide the listbox

def copy_to_clipboard(data):
    """Copy the given data to the clipboard."""
    root.clipboard_clear()
    root.clipboard_append(data)
    root.update()  # Keep the clipboard content available
    messagebox.showinfo("Copied", "Data copied to clipboard!")

def copy_title():
    """Copy the title of the selected row."""
    selected_item = tree.selection()
    if selected_item:
        title = tree.item(selected_item[0], "values")[1]  # Title is now in column 1
        copy_to_clipboard(title)

def copy_keywords():
    """Copy the keywords of the selected row."""
    selected_item = tree.selection()
    if selected_item:
        keywords = tree.item(selected_item[0], "values")[2]  # Keywords is now in column 2
        copy_to_clipboard(keywords)

def copy_description():
    """Copy the description of the selected row."""
    selected_item = tree.selection()
    if selected_item:
        description = tree.item(selected_item[0], "values")[3]  # Description is in column 3
        copy_to_clipboard(description)

def copy_metadata():
    """Copy title, keywords, and description as metadata."""
    selected_item = tree.selection()
    if selected_item:
        title = tree.item(selected_item[0], "values")[1]
        keywords = tree.item(selected_item[0], "values")[2]
        description = tree.item(selected_item[0], "values")[3]
        metadata = f"Title: {title}\nKeywords: {keywords}\nDescription: {description}"
        copy_to_clipboard(metadata)

def regenerate_metadata(item_id_to_regen): # Renamed parameter
    """Regenerate metadata for a single item."""
    filename = tree.item(item_id_to_regen, "values")[0]

    # Find original file path
    file_path = None
    for i in range(image_listbox.size()):
        if os.path.basename(image_listbox.get(i)) == filename:
            file_path = image_listbox.get(i)
            break

    if file_path:
        process_single_image(file_path) # This will update the treeview item
    else:
        messagebox.showerror("Error", f"Original file not found: {filename}")


# Modify the context menu creation
context_menu = tk.Menu(tree, tearoff=0)
context_menu.add_command(label="Regenerate", command=lambda: regenerate_metadata(tree.selection()[0]) if tree.selection() else None) # Added check for selection
context_menu.add_separator()
context_menu.add_command(label="Find & Replace", command=find_replace_metadata)
context_menu.add_separator()
context_menu.add_command(label="Copy Title", command=copy_title)
context_menu.add_command(label="Copy Keywords", command=copy_keywords)
context_menu.add_command(label="Copy Description", command=copy_description)
context_menu.add_command(label="Copy All Metadata", command=copy_metadata)

# Bind right-click to show the context menu
def show_context_menu(event):
    """Show the context menu for copying data."""
    selected_item = tree.identify_row(event.y)
    if selected_item:
        tree.selection_set(selected_item)
        context_menu.post(event.x_root, event.y_root)

def show_metadata_editor(event):
    """Display metadata editor window when double-clicking a treeview item."""
    selected_item = tree.selection()
    if not selected_item:
        return

    item_id_editor = selected_item[0] # Renamed variable
    item_values = tree.item(item_id_editor, "values")

    if len(item_values) == 6:
        filename, title, keywords, description, category_editor, rating_editor = item_values
    elif len(item_values) == 5:
        filename, title, keywords, description, category_editor = item_values
        rating_editor = "★★★★★" # Default rating if missing
        print(f"Warning: Metadata item '{filename}' in editor had 5 values. Defaulting rating to '{rating_editor}'.")
    elif len(item_values) == 4:
        filename, title, keywords, description = item_values
        category_editor = "N/A" # Default category if missing
        rating_editor = "★★★★★" # Default rating if missing
        print(f"Warning: Metadata item '{filename}' in editor had 4 values. Defaulting category to '{category_editor}' and rating to '{rating_editor}'.")
        # Optionally, update the tree item here to fix it for the current session:
        # tree.item(item_id_editor, values=(filename, title, keywords, description, category_editor, rating_editor))
    else:
        # Try to get filename for error message if possible
        error_filename = item_values[0] if item_values and len(item_values) > 0 else "Unknown Item"
        messagebox.showerror("Data Error", f"Unexpected number of metadata values ({len(item_values)}) for item '{error_filename}'. Cannot open editor.")
        return

    # Create editor window
    editor_window = ttk.Toplevel(root)
    editor_window.title(f"Metadata Editor - {filename}")
    editor_window.geometry("800x600")

    # Create main frame
    editor_frame = ttk.Frame(editor_window, padding="10")
    editor_frame.pack(fill="both", expand=True)

    # Add image preview
    preview_frame = ttk.Frame(editor_frame)
    preview_frame.pack(fill="x", pady=(0, 10))

    # Find original path and display preview
    file_path_editor = None # Renamed variable
    for i in range(image_listbox.size()):
        if os.path.basename(image_listbox.get(i)) == filename:
            file_path_editor = image_listbox.get(i)
            break

    if file_path_editor and os.path.exists(file_path_editor):
        try:
            preview_path_for_display = file_path_editor # Path to use for opening image
            temp_preview_file = None # For SVG/EPS conversion cleanup

            # Handle vector files
            if file_path_editor.lower().endswith('.svg'):
                temp_preview_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False).name
                convert_svg_to_png(file_path_editor, temp_preview_file)
                preview_path_for_display = temp_preview_file
            elif file_path_editor.lower().endswith('.eps'):
                temp_preview_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False).name
                convert_eps_to_png(file_path_editor, temp_preview_file)
                preview_path_for_display = temp_preview_file
            elif file_path_editor.lower().endswith('.ai'): # AI preview
                temp_preview_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False).name
                if convert_ai_to_png_with_illustrator(file_path_editor, temp_preview_file):
                    preview_path_for_display = temp_preview_file
                else: # Fallback if AI conversion fails
                    ttk.Label(preview_frame, text="AI Preview not available (Illustrator error or not found)").pack()
                    preview_path_for_display = None # Prevent trying to open
            elif file_path_editor.lower().endswith(('.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.asf', '.m2v', '.mpg', '.mpeg')):
                # Video preview - extract a frame
                temp_preview_file = tempfile.NamedTemporaryFile(suffix=".jpg", delete=False).name
                try:
                    frame_path = extract_frame_from_video(file_path_editor, temp_preview_file, time_seconds=3)
                    if frame_path and os.path.exists(frame_path):
                        preview_path_for_display = frame_path
                    else:
                        ttk.Label(preview_frame, text="🎬 Video Preview\n(Frame extraction failed)", justify="center").pack(expand=True)
                        preview_path_for_display = None
                except Exception as e:
                    ttk.Label(preview_frame, text=f"🎬 Video Preview\n(Error: {str(e)[:50]}...)", justify="center").pack(expand=True)
                    preview_path_for_display = None


            if preview_path_for_display:
                img = Image.open(preview_path_for_display)
                img.thumbnail((300, 300))
                img_tk = ImageTk.PhotoImage(img)
                img_label = ttk.Label(preview_frame, image=img_tk)
                img_label.image = img_tk
                img_label.pack()

            if temp_preview_file and os.path.exists(temp_preview_file): # Cleanup temp file
                try:
                    os.remove(temp_preview_file)
                except Exception as e_clean:
                    print(f"Error cleaning temp preview file: {e_clean}")


        except Exception as e:
            ttk.Label(preview_frame, text="Preview not available").pack()
            print(f"Error generating preview in editor: {e}")


    # Add metadata fields
    metadata_frame = ttk.Frame(editor_frame)
    metadata_frame.pack(fill="both", expand=True, pady=(10, 0))

    # Title section
    title_frame = ttk.LabelFrame(metadata_frame, text="Title", padding="5")
    title_frame.pack(fill="x", pady=(0, 5))

    title_entry = ttk.Entry(title_frame, width=70)
    title_entry.insert(0, title)
    title_entry.pack(side="left", padx=(5, 5))

    copy_title_btn = ttk.Button(title_frame, text="Copy",
                               command=lambda: copy_to_clipboard(title_entry.get()))
    copy_title_btn.pack(side="left")

    # Keywords section
    keywords_frame = ttk.LabelFrame(metadata_frame, text="Keywords", padding="5")
    keywords_frame.pack(fill="both", expand=True, pady=(5, 5))

    keywords_text = tk.Text(keywords_frame, height=10, width=70) # Changed to tk.Text for multi-line
    keywords_text.insert("1.0", keywords)
    keywords_text.pack(side="left", padx=(5, 5), fill="both", expand=True)


    copy_keywords_btn = ttk.Button(keywords_frame, text="Copy",
                                  command=lambda: copy_to_clipboard(keywords_text.get("1.0", "end-1c")))
    copy_keywords_btn.pack(side="left")

    # Description section
    description_frame = ttk.LabelFrame(metadata_frame, text=f"Description ({max_description_var.get()} characters max)", padding="5")
    description_frame.pack(fill="both", expand=True, pady=(5, 5))

    # Description is taken from the tree values.
    # If it was "Pending..." or empty, process_single_image should have filled it or left it blank if descriptions disabled.
    if not description_enabled.get():
        description = ""

    description_text = tk.Text(description_frame, height=4, width=70)
    description_text.insert("1.0", description)
    description_text.pack(side="left", padx=(5, 5), fill="both", expand=True)


    # Add character counter for description
    max_desc_chars = max_description_var.get()
    char_count = len(description)
    char_count_var = tk.StringVar(value=f"Characters: {char_count}/{max_desc_chars}")
    char_count_label = ttk.Label(description_frame, textvariable=char_count_var)
    char_count_label.pack(side="top", padx=(5, 5))

    # Update character count when description changes
    def update_char_count(event=None):
        current_text = description_text.get("1.0", "end-1c")
        max_chars = max_description_var.get()
        current_char_count = len(current_text)
        char_count_var.set(f"Characters: {current_char_count}/{max_chars}")
        if current_char_count > max_chars:
            char_count_label.config(foreground="red")
        else:
            char_count_label.config(foreground="")

    description_text.bind("<KeyRelease>", update_char_count)

    copy_description_btn = ttk.Button(description_frame, text="Copy",
                                    command=lambda: copy_to_clipboard(description_text.get("1.0", "end-1c")))
    copy_description_btn.pack(side="left")

    # Rating section
    rating_frame = ttk.LabelFrame(metadata_frame, text="Rating", padding="5")
    rating_frame.pack(fill="x", pady=(5, 5))

    # Create rating variable and buttons
    rating_var = tk.StringVar(value=rating_editor)

    def update_rating(stars):
        """Update the rating display."""
        rating_var.set(stars)
        # Update button styles to show selected rating
        for i, btn in enumerate(rating_buttons):
            if i < len(stars.replace("☆", "")):
                btn.config(bootstyle="warning")  # Filled stars
            else:
                btn.config(bootstyle="outline-warning")  # Empty stars

    # Create star rating buttons
    rating_buttons = []
    star_frame = ttk.Frame(rating_frame)
    star_frame.pack(side="left", padx=(5, 5))

    ratings = ["★☆☆☆☆", "★★☆☆☆", "★★★☆☆", "★★★★☆", "★★★★★"]
    for i, stars in enumerate(ratings):
        btn = ttk.Button(star_frame, text=f"{i+1}★", width=4,
                        command=lambda s=stars: update_rating(s))
        btn.pack(side="left", padx=1)
        rating_buttons.append(btn)

    # Initialize rating display
    update_rating(rating_editor)

    # Rating display label
    rating_display = ttk.Label(rating_frame, textvariable=rating_var, font=("Arial", 12))
    rating_display.pack(side="left", padx=(10, 5))

    def update_metadata():
        """Update metadata in treeview."""
        new_title = title_entry.get().strip()
        new_keywords = keywords_text.get("1.0", "end-1c").strip()
        new_description = description_text.get("1.0", "end-1c").strip()
        new_rating = rating_var.get()

        # Limit to max_description_var characters
        max_chars = max_description_var.get()
        if len(new_description) > max_chars:
            new_description = new_description[:max_chars-3] + "..."

        # Update treeview with values including description, category, and rating
        tree.item(item_id_editor, values=(filename, new_title, new_keywords, new_description, category_editor, new_rating))
        editor_window.destroy()

    button_frame = ttk.Frame(editor_frame)
    button_frame.pack(fill="x", pady=(10, 0))

    def save_and_embed():
        """Update metadata in treeview and embed it."""
        new_title = title_entry.get().strip()
        new_keywords = keywords_text.get("1.0", "end-1c").strip()
        new_description = description_text.get("1.0", "end-1c").strip()
        new_rating = rating_var.get()
        max_chars = max_description_var.get()
        if len(new_description) > max_chars:
            new_description = new_description[:max_chars-3] + "..."

        # Update treeview with values including description, category, and rating
        tree.item(item_id_editor, values=(filename, new_title, new_keywords, new_description, category_editor, new_rating))


        # Embed metadata based on file type
        if file_path_editor: # Check if file_path_editor was found
            if file_path_editor.lower().endswith(('.jpg', '.jpeg', '.png')):
                # Call embed_metadata_with_exiftool which iterates, so pass specific file
                keyword_list_embed = [kw.strip() for kw in re.split(r'[;,|]', new_keywords) if kw.strip()]
                embed_metadata(file_path_editor, new_title, new_description, keyword_list_embed, new_rating)
                messagebox.showinfo("✅ Success", "Image Metadata embedded successfully!")
            elif file_path_editor.lower().endswith(('.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.asf', '.m2v', '.mpg', '.mpeg')):
                # Video file metadata embedding
                keyword_list_embed = [kw.strip() for kw in re.split(r'[;,|]', new_keywords) if kw.strip()]
                embed_metadata(file_path_editor, new_title, new_description, keyword_list_embed, new_rating)
                messagebox.showinfo("✅ Success", "Video Metadata embedded successfully!")
            elif file_path_editor.lower().endswith(('.svg', '.eps', '.ai')):
                # embed_metadata_with_illustrator iterates, this might be tricky.
                # For now, let's assume user will use the main button for batch illustrator embedding.
                # Or, we adapt it to handle a single file.
                # For simplicity, just show a message.
                messagebox.showinfo("Info", "For vector files, please use the main 'Export JPG & Embed Vector' button after saving edits.")

        editor_window.destroy()

    save_btn = ttk.Button(button_frame, text="Save Edit", command=update_metadata, bootstyle=PRIMARY)
    save_btn.pack(side="right", padx=(5, 0))

    save_embed_btn = ttk.Button(button_frame, text="Save & Embed", command=save_and_embed, bootstyle=SUCCESS)
    save_embed_btn.pack(side="right", padx=(5, 0))

    cancel_btn = ttk.Button(button_frame, text="Cancel", command=editor_window.destroy, bootstyle=DANGER)
    cancel_btn.pack(side="right", padx=(5, 5))

# Bind double-click on treeview to metadata editor (already bound above)

progress_bar = ttk.Progressbar(root, mode="determinate")
progress_bar.pack(fill=tk.X, pady=(5, 0))

# Create a more organized status frame with two rows
status_frame = ttk.Frame(root)
status_frame.pack(fill=tk.X, pady=(5, 2))

# Top row for status message
status_message_frame = ttk.Frame(status_frame)
status_message_frame.pack(fill=tk.X, pady=(0, 2))

# Status message with icon
status_message_label = ttk.Label(status_message_frame, text="Ready", font=("Arial", 10, "bold"))
status_message_label.pack(side=tk.LEFT, padx=(5, 0))

# Bottom row for counters and timers
counters_frame = ttk.Frame(status_frame)
counters_frame.pack(fill=tk.X)

# Left side: File counters
counter_frame = ttk.Frame(counters_frame)
counter_frame.pack(side=tk.LEFT, fill=tk.Y)

selected_label = ttk.Label(counter_frame, text="Selected: 0")
selected_label.pack(side=tk.LEFT, padx=(5, 10))

processed_label = ttk.Label(counter_frame, text="Processed: 0/0 (0%)")
processed_label.pack(side=tk.LEFT, padx=(0, 10))

remaining_label = ttk.Label(counter_frame, text="Remaining: 0")
remaining_label.pack(side=tk.LEFT, padx=(0, 10))

# Right side: Time tracking
time_frame = ttk.Frame(counters_frame)
time_frame.pack(side=tk.RIGHT, fill=tk.Y)

elapsed_label = ttk.Label(time_frame, text="Elapsed: 00:00")
elapsed_label.pack(side=tk.LEFT, padx=(0, 10))

total_time_label = ttk.Label(time_frame, text="Total: 00:00")
total_time_label.pack(side=tk.LEFT, padx=(0, 5))

# Create a combined frame for stats and license info
info_frame = ttk.Frame(root, padding=(5, 0, 5, 0))
info_frame.pack(fill=tk.X, pady=(0, 0))

# Add a separator line between the two sections
separator = ttk.Separator(info_frame, orient="vertical")

# Left side: Statistics section
stats_section = ttk.Frame(info_frame)
stats_section.pack(side=tk.LEFT, fill=tk.Y)

ttk.Label(stats_section, text="📊 Generation Statistics:", font=("Arial", 12, "bold")).pack(side=tk.LEFT, padx=(5, 5))

stats_24h_label = ttk.Label(stats_section, text="Last 24 Hours: 0 files", font=("Arial", 10))
stats_24h_label.pack(side=tk.LEFT, padx=(5, 5))

stats_total_label = ttk.Label(stats_section, text="All Time: 0 files", font=("Arial", 10))
stats_total_label.pack(side=tk.LEFT, padx=(5, 5))

# Add separator
separator.pack(side=tk.LEFT, fill=tk.Y, padx=(5, 5), pady=2)

# Right side: License section
license_section = ttk.Frame(info_frame)
license_section.pack(side=tk.LEFT, fill=tk.Y)

ttk.Label(license_section, text="🔑 License Information", font=("Arial", 12, "bold")).pack(side=tk.LEFT, padx=(5, 5))

def init_stats_db():
    """Initialize the statistics database."""
    try:
        # Ensure the MetaMaster directory exists
        appdata_dir = os.path.join(os.getenv("APPDATA"), "MetaMaster")
        os.makedirs(appdata_dir, exist_ok=True)

        db_path = os.path.join(appdata_dir, "stats.db")
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS metadata_generations
                     (timestamp TEXT, filename TEXT)''')
        conn.commit()
        conn.close()
        print(f"📊 Statistics database initialized at: {db_path}")
    except Exception as e:
        print(f"❌ Error initializing statistics database: {e}")

def update_stats(filename):
    """Record a metadata generation in the database."""
    try:
        # Ensure the MetaMaster directory exists
        appdata_dir = os.path.join(os.getenv("APPDATA"), "MetaMaster")
        os.makedirs(appdata_dir, exist_ok=True)

        db_path = os.path.join(appdata_dir, "stats.db")
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        c.execute("INSERT INTO metadata_generations VALUES (?, ?)",
                  (datetime.now().isoformat(), filename))
        conn.commit()
        conn.close()
        print(f"📊 Statistics updated for: {filename}")
    except Exception as e:
        print(f"❌ Error updating statistics for {filename}: {e}")

def get_stats():
    """Get metadata generation statistics."""
    try:
        # Ensure the MetaMaster directory exists
        appdata_dir = os.path.join(os.getenv("APPDATA"), "MetaMaster")
        os.makedirs(appdata_dir, exist_ok=True)

        db_path = os.path.join(appdata_dir, "stats.db")
        conn = sqlite3.connect(db_path)
        c = conn.cursor()

        # Get last 24 hours count
        yesterday = (datetime.now() - timedelta(days=1)).isoformat()
        c.execute("SELECT COUNT(*) FROM metadata_generations WHERE timestamp > ?",
                  (yesterday,))
        last_24h = c.fetchone()[0]

        # Get all time count
        c.execute("SELECT COUNT(*) FROM metadata_generations")
        all_time = c.fetchone()[0]

        conn.close()
        print(f"📊 Statistics retrieved: {last_24h} (24h), {all_time} (all time)")
        return last_24h, all_time
    except Exception as e:
        print(f"❌ Error getting statistics: {e}")
        return 0, 0  # Return default values on error

def update_stats_labels():
    """Update the statistics labels in the UI."""
    try:
        last_24h, all_time = get_stats()
        if 'stats_24h_label' in globals() and stats_24h_label.winfo_exists():
            stats_24h_label.config(text=f"Last 24 Hours: {last_24h} files")
        if 'stats_total_label' in globals() and stats_total_label.winfo_exists():
            stats_total_label.config(text=f"All Time: {all_time} files")
        print(f"📊 Statistics labels updated: {last_24h} (24h), {all_time} (all time)")
    except Exception as e:
        print(f"❌ Error updating statistics labels: {e}")
    finally:
        # Schedule next update regardless of errors
        root.after(60000, update_stats_labels)  # Update every minute

# Initialize the database and start updating stats
init_stats_db()
root.after(500, update_stats_labels)

license_status_label = ttk.Label(license_section, text="", font=("Arial", 10))
license_status_label.pack(side=tk.LEFT, padx=(5, 5))

license_message_label = ttk.Label(license_section, text="", font=("Arial", 10))
license_message_label.pack(side=tk.LEFT, padx=(5, 5))

ttk.Button(license_section, text="Refresh", bootstyle=PRIMARY, command=show_license_status).pack(side=tk.RIGHT, padx=(5, 5))


def periodic_license_check():
    """Periodically check the license status every 10 minutes."""
    show_license_status()  # Refresh license status
    root.after(10 * 60 * 1000, periodic_license_check)  # Schedule next check in 10 minutes

# Ensure license status is checked periodically
root.after(500, periodic_license_check)

# Ensure license status is checked after UI is fully loaded
root.after(500, show_license_status)
root.after(500, check_and_show_license)

def show_file_preview(event):
    """Display a preview of the selected file."""
    selected_index = image_listbox.curselection()
    if not selected_index:
        return

    file_path_preview = image_listbox.get(selected_index[0]) # Renamed variable
    if not os.path.exists(file_path_preview):
        messagebox.showerror("Error", f"File not found: {file_path_preview}")
        return

    try:
        if file_path_preview.lower().endswith(('.png', '.jpg', '.jpeg', '.svg', '.eps', '.ai')): # Added .ai
            preview_window = tk.Toplevel(root)
            preview_window.title(f"Preview - {os.path.basename(file_path_preview)}")
            preview_window.geometry("600x600")

            preview_path_for_display = file_path_preview
            temp_preview_file = None


            # Convert SVG/EPS/AI to PNG for preview
            if file_path_preview.lower().endswith('.svg'):
                temp_preview_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False).name
                convert_svg_to_png(file_path_preview, temp_preview_file)
                preview_path_for_display = temp_preview_file
            elif file_path_preview.lower().endswith('.eps'):
                temp_preview_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False).name
                convert_eps_to_png(file_path_preview, temp_preview_file)
                preview_path_for_display = temp_preview_file
            elif file_path_preview.lower().endswith('.ai'):
                temp_preview_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False).name
                if convert_ai_to_png_with_illustrator(file_path_preview, temp_preview_file):
                    preview_path_for_display = temp_preview_file
                else:
                    preview_path_for_display = None # Indicate failure
                    ttk.Label(preview_window, text="AI Preview failed (Illustrator error or not found).").pack()


            if preview_path_for_display:
                img = Image.open(preview_path_for_display)
                img.thumbnail((600, 600))  # Resize for preview
                img_tk = ImageTk.PhotoImage(img)

                label = ttk.Label(preview_window, image=img_tk)
                label.image = img_tk  # Keep a reference to avoid garbage collection
                label.pack(expand=True, fill=tk.BOTH)
            
            if temp_preview_file and os.path.exists(temp_preview_file):
                try:
                    os.remove(temp_preview_file)
                except Exception as e_clean_preview:
                    print(f"Error cleaning temp preview file: {e_clean_preview}")


            preview_window.mainloop() # This blocks, consider if this is intended for a quick preview
        else:
            messagebox.showinfo("Preview", f"Preview not supported for this file type: {os.path.basename(file_path_preview)}")
    except Exception as e:
        messagebox.showerror("Error", f"Failed to preview file: {e}")

# Bind double-click and hover events to the listbox
image_listbox.bind("<Double-1>", show_file_preview)
image_listbox.bind("<Enter>", lambda event: image_listbox.config(cursor="hand2"))
image_listbox.bind("<Leave>", lambda event: image_listbox.config(cursor="arrow"))

# Function to handle rating clicks
def on_tree_click(event):
    """Handle clicks on the treeview, especially for rating column."""
    region = tree.identify_region(event.x, event.y)
    if region == "cell":
        column = tree.identify_column(event.x)
        if column == "#6":  # Rating column (6th column)
            item = tree.identify_row(event.y)
            if item:
                show_rating_popup(item, event)

def show_rating_popup(item, event):
    """Show a popup to change the rating."""
    # Create popup menu
    rating_menu = tk.Menu(root, tearoff=0)

    ratings = [
        ("1 Star - ★☆☆☆☆", "★☆☆☆☆"),
        ("2 Stars - ★★☆☆☆", "★★☆☆☆"),
        ("3 Stars - ★★★☆☆", "★★★☆☆"),
        ("4 Stars - ★★★★☆", "★★★★☆"),
        ("5 Stars - ★★★★★", "★★★★★")
    ]

    def update_rating(new_rating):
        """Update the rating for the selected item."""
        values = list(tree.item(item)["values"])
        if len(values) >= 6:
            values[5] = new_rating  # Update rating column
            tree.item(item, values=tuple(values))

    for label, rating in ratings:
        rating_menu.add_command(label=label, command=lambda r=rating: update_rating(r))

    # Show the popup menu
    rating_menu.post(event.x_root, event.y_root)

# Bind events to the tree
tree.bind("<Button-3>", show_context_menu)
tree.bind("<Double-1>", show_metadata_editor)
tree.bind("<Button-1>", on_tree_click)  # Add click handler for rating





# ===== PROMPT GENERATOR IMPLEMENTATION =====
# Create the prompt generator frame (initially hidden)
prompt_generator_frame = ttk.Frame(root)
prompt_generator_frame.pack_forget()

# Global variables for prompt generator
monitoring_pg = False
local_images_pg = []

# Create UI elements for prompt generator with same design as metadata generator
# Professional branding header for prompt generator
branding_frame_pg = ttk.Frame(prompt_generator_frame, padding=(0, 2, 0, 5))
branding_frame_pg.pack(fill=tk.X, pady=(0, 10))
branding_frame_pg.columnconfigure(0, weight=1)
branding_frame_pg.columnconfigure(1, weight=0)

# Left side: Logo and branding
branding_left_pg = ttk.Frame(branding_frame_pg)
branding_left_pg.grid(row=0, column=0, sticky="w", padx=(0,10))

# Load the branding PNG image
icon_path_png_brand_pg = resource_path("Meta Master.png")
try:
    brand_image_pg = Image.open(icon_path_png_brand_pg)
    brand_image_pg = brand_image_pg.resize((40, 40), Image.Resampling.LANCZOS)
    brand_photo_pg = ImageTk.PhotoImage(brand_image_pg)
    brand_label_pg = ttk.Label(branding_left_pg, image=brand_photo_pg)
    brand_label_pg.image = brand_photo_pg
    brand_label_pg.pack(side=tk.LEFT, padx=(0, 10))
except Exception as e:
    print(f"Could not load brand image: {e}")

# Title and subtitle
title_label_pg = ttk.Label(branding_left_pg, text="Meta Master", font=("Segoe UI", 16, "bold"))
title_label_pg.pack(side=tk.LEFT, anchor="w")
subtitle_label_pg = ttk.Label(branding_left_pg, text="Prompt Generator Mode", font=("Segoe UI", 10), foreground="#666666")
subtitle_label_pg.pack(side=tk.LEFT, anchor="w", padx=(10, 0))

# Right side: Mode switch button
mode_switch_frame_pg = ttk.Frame(branding_frame_pg)
mode_switch_frame_pg.grid(row=0, column=1, sticky="e")

def switch_to_metadata_mode():
    mode_var.set("Image")  # Switch back to Image mode
    toggle_mode()

switch_mode_btn_pg = ttk.Button(mode_switch_frame_pg, text="← Back to Metadata Generator",
                               command=switch_to_metadata_mode, bootstyle="outline-primary")
switch_mode_btn_pg.pack(side=tk.RIGHT, padx=5)

# Main Controls Container with Professional Layout
main_controls_frame_pg = ttk.Frame(prompt_generator_frame)
main_controls_frame_pg.pack(fill=tk.X, pady=(10, 5), padx=10)

# Create a professional card-style layout
controls_card_pg = ttk.LabelFrame(main_controls_frame_pg, text="🎯 Prompt Configuration", padding=(15, 10))
controls_card_pg.pack(fill=tk.X, pady=(0, 10))

# Row 1: Primary Settings - Side by Side Layout
primary_row_pg = ttk.Frame(controls_card_pg)
primary_row_pg.pack(fill=tk.X, pady=(0, 8))

# Left Side: Prompt Mode Selection
mode_section_pg = ttk.Frame(primary_row_pg)
mode_section_pg.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

mode_label_pg = ttk.Label(mode_section_pg, text="Prompt Style", font=("Segoe UI", 10, "bold"))
mode_label_pg.pack(anchor="w", pady=(0, 3))

mode_var_pg = tk.StringVar(value="Image Analysis")
mode_menu_pg = ttk.Combobox(mode_section_pg, textvariable=mode_var_pg,
                           values=["Image Analysis", "Vector Description", "Illustration Mode", "Icon Description",
                                  "Vector Set", "Icon Set", "Isolated Background", "Technology Images",
                                  "Ultra Descriptive", "Creative Storytelling", "Technical Analysis", "Marketing Copy",
                                  "MidJourney Pro", "DALL-E 3 Optimized", "Stable Diffusion", "Product Photography",
                                  "Artistic Vision", "Cinematic Scene", "Character Design", "Environment Design",
                                  "Stock Photo", "Social Media", "E-commerce"],
                           state="readonly", width=22, font=("Segoe UI", 9))
mode_menu_pg.pack(fill=tk.X, pady=(0, 3))

mode_desc_label_pg = ttk.Label(mode_section_pg, text="Analyze and describe image content",
                              font=("Segoe UI", 8), foreground="#666666")
mode_desc_label_pg.pack(anchor="w")

# Right Side: Quality Enhancements
enhancements_section_pg = ttk.Frame(primary_row_pg)
enhancements_section_pg.pack(side=tk.LEFT, fill=tk.Y, padx=(10, 0))

enhancements_label_pg = ttk.Label(enhancements_section_pg, text="Quality Enhancements", font=("Segoe UI", 10, "bold"))
enhancements_label_pg.pack(anchor="w", pady=(0, 3))

# Enhancement checkboxes in a compact grid layout
enhancement_grid_pg = ttk.Frame(enhancements_section_pg)
enhancement_grid_pg.pack(fill=tk.X)

# Row 1 of enhancements
enhancement_row1_pg = ttk.Frame(enhancement_grid_pg)
enhancement_row1_pg.pack(fill=tk.X, pady=(0, 2))

quality_var_pg = tk.BooleanVar()
quality_check_pg = ttk.Checkbutton(enhancement_row1_pg, text="✨ Quality", variable=quality_var_pg,
                                  bootstyle="round-toggle", width=12)
quality_check_pg.pack(side=tk.LEFT, padx=(0, 8))

cinematic_var_pg = tk.BooleanVar()
cinematic_check_pg = ttk.Checkbutton(enhancement_row1_pg, text="🎬 Cinematic", variable=cinematic_var_pg,
                                    bootstyle="round-toggle", width=12)
cinematic_check_pg.pack(side=tk.LEFT)

# Row 2 of enhancements
enhancement_row2_pg = ttk.Frame(enhancement_grid_pg)
enhancement_row2_pg.pack(fill=tk.X)

photo_var_pg = tk.BooleanVar()
photo_check_pg = ttk.Checkbutton(enhancement_row2_pg, text="📸 Pro Photo", variable=photo_var_pg,
                                bootstyle="round-toggle", width=12)
photo_check_pg.pack(side=tk.LEFT, padx=(0, 8))

mj_format_var_pg = tk.BooleanVar(value=False)
mj_toggle_btn = ttk.Checkbutton(enhancement_row2_pg, text="🚀 MidJourney", variable=mj_format_var_pg,
                               bootstyle="round-toggle", width=12)
mj_toggle_btn.pack(side=tk.LEFT)

# Function to update mode description
def update_mode_description_pg(*args):
    """Update the mode description based on selected mode."""
    mode_descriptions = {
        # Image-Specific Analysis Modes
        "Image Analysis": "Analyze and describe exactly what's visible in the image content",
        "Vector Description": "Describe vector graphics with clean lines and scalable elements",
        "Illustration Mode": "Analyze artistic illustrations with style and technique details",
        "Icon Description": "Describe icons with symbolic meaning and design elements",
        "Vector Set": "Describe collections of related vector graphics and their themes",
        "Icon Set": "Analyze icon collections with consistent style and purpose",
        "Isolated Background": "Describe objects on white/transparent backgrounds for cutouts",
        "Technology Images": "Analyze tech-related images with technical specifications",

        # Professional AI Generation Modes
        "Ultra Descriptive": "Hyperrealistic, cinematic prompts with precise visual storytelling",
        "Creative Storytelling": "Narrative-driven artistic prompts with emotional depth",
        "Technical Analysis": "Professional photographer's technical perspective",
        "Marketing Copy": "Commercial and advertising-focused prompts",
        "MidJourney Pro": "Advanced MidJourney prompts with professional parameters",
        "DALL-E 3 Optimized": "DALL-E 3 specific optimization for clarity",
        "Stable Diffusion": "Token weighting and optimization for Stable Diffusion",
        "Product Photography": "Commercial product photography specifications",
        "Artistic Vision": "Fine art and gallery-worthy artistic prompts",
        "Cinematic Scene": "Film director's approach to scene creation",
        "Character Design": "Game and animation character design",
        "Environment Design": "World-building and architectural visualization",
        "Stock Photo": "Commercial stock photography optimization",
        "Social Media": "Viral social media content optimization",
        "E-commerce": "Online retail product presentation"
    }

    selected_mode = mode_var_pg.get()
    description = mode_descriptions.get(selected_mode, "Professional AI image generation prompts")
    mode_desc_label_pg.config(text=description)

# Bind the function to mode selection change
mode_var_pg.trace('w', update_mode_description_pg)
update_mode_description_pg()  # Set initial description

# Removed AI Platform Selection - not needed

# Row 2: Word Limit and Custom Instructions
secondary_row_pg = ttk.Frame(controls_card_pg)
secondary_row_pg.pack(fill=tk.X, pady=(5, 8))

# Left Side: Word Limit Control
words_section_pg = ttk.Frame(secondary_row_pg)
words_section_pg.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

words_label_pg = ttk.Label(words_section_pg, text="Word Limit", font=("Segoe UI", 10, "bold"))
words_label_pg.pack(anchor="w", pady=(0, 3))

max_words_var_pg = tk.IntVar(value=100)
max_words_spinbox_pg = ttk.Spinbox(words_section_pg, from_=10, to=1000, textvariable=max_words_var_pg,
                                  width=8, font=("Segoe UI", 9))
max_words_spinbox_pg.pack(pady=(0, 3))

words_desc_label_pg = ttk.Label(words_section_pg, text="10-1000 words",
                               font=("Segoe UI", 8), foreground="#666666")
words_desc_label_pg.pack(anchor="w")

# Right Side: Custom Instructions
custom_section_pg = ttk.Frame(secondary_row_pg)
custom_section_pg.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0))

custom_label_pg = ttk.Label(custom_section_pg, text="Custom Instructions (Optional)", font=("Segoe UI", 10, "bold"))
custom_label_pg.pack(anchor="w", pady=(0, 3))

custom_prompt_entry_pg = ttk.Entry(custom_section_pg, width=50, font=("Segoe UI", 9))
custom_prompt_entry_pg.pack(fill=tk.X, pady=(0, 3))

custom_desc_label_pg = ttk.Label(custom_section_pg, text="Add specific instructions or override the selected prompt mode",
                                font=("Segoe UI", 8), foreground="#666666")
custom_desc_label_pg.pack(anchor="w")

# Duplicate sections removed - using side-by-side layout above

# Action Buttons Section with Professional Layout
action_section_pg = ttk.Frame(prompt_generator_frame)
action_section_pg.pack(fill=tk.X, pady=(0, 10), padx=10)

# Action buttons card
action_card_pg = ttk.LabelFrame(action_section_pg, text="⚡ Actions", padding=(15, 10))
action_card_pg.pack(fill=tk.X)

# Primary actions row
primary_actions_pg = ttk.Frame(action_card_pg)
primary_actions_pg.pack(fill=tk.X, pady=(0, 10))

# Create button frames first (buttons will be added after function definitions)
file_section_pg = ttk.Frame(primary_actions_pg)
file_section_pg.pack(side=tk.LEFT, fill=tk.Y)

process_section_pg = ttk.Frame(primary_actions_pg)
process_section_pg.pack(side=tk.LEFT, fill=tk.Y)

utility_section_pg = ttk.Frame(primary_actions_pg)
utility_section_pg.pack(side=tk.RIGHT, fill=tk.Y)

# Data Display Section with Professional Layout
data_section_pg = ttk.Frame(prompt_generator_frame)
data_section_pg.pack(fill=tk.BOTH, expand=True, pady=(0, 10), padx=10)

# Data display card
data_card_pg = ttk.LabelFrame(data_section_pg, text="📋 Generated Prompts", padding=(15, 10))
data_card_pg.pack(fill=tk.BOTH, expand=True)

# Status bar
status_frame_pg = ttk.Frame(data_card_pg)
status_frame_pg.pack(fill=tk.X, pady=(0, 10))

status_label_pg = ttk.Label(status_frame_pg, text="Ready to process images",
                           font=("Segoe UI", 9), foreground="#666666")
status_label_pg.pack(side=tk.LEFT)

# Progress indicator
progress_frame_pg = ttk.Frame(status_frame_pg)
progress_frame_pg.pack(side=tk.RIGHT)

progress_label_pg = ttk.Label(progress_frame_pg, text="0 processed",
                             font=("Segoe UI", 9), foreground="#666666")
progress_label_pg.pack(side=tk.RIGHT)

# Treeview with enhanced styling
treeview_frame_pg = ttk.Frame(data_card_pg)
treeview_frame_pg.pack(fill=tk.BOTH, expand=True)

# Create the treeview with professional styling including copy action column
generated_data_tree_pg = ttk.Treeview(treeview_frame_pg, columns=("Filename", "Mode", "Description", "Action"),
                                     show="headings", height=12)
generated_data_tree_pg.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

# Configure column headings with enhanced styling
generated_data_tree_pg.heading("Filename", text="📁 File Name")
generated_data_tree_pg.heading("Mode", text="🎯 Prompt Mode")
generated_data_tree_pg.heading("Description", text="📝 Generated Prompt")
generated_data_tree_pg.heading("Action", text="⚡ Action")

# Configure column widths for better readability
generated_data_tree_pg.column("Filename", width=180, minwidth=120, anchor="w")
generated_data_tree_pg.column("Mode", width=150, minwidth=100, anchor="center")
generated_data_tree_pg.column("Description", width=500, minwidth=350, anchor="w")
generated_data_tree_pg.column("Action", width=80, minwidth=80, anchor="center")

# Add professional scrollbars
v_scrollbar_pg = ttk.Scrollbar(treeview_frame_pg, orient="vertical", command=generated_data_tree_pg.yview)
v_scrollbar_pg.pack(side=tk.RIGHT, fill=tk.Y)
generated_data_tree_pg.configure(yscrollcommand=v_scrollbar_pg.set)

# Configure treeview styling
generated_data_tree_pg.tag_configure('evenrow', background='#f8f9fa')
generated_data_tree_pg.tag_configure('oddrow', background='#ffffff')
generated_data_tree_pg.tag_configure('processing', background='#fff3cd', foreground='#856404')
generated_data_tree_pg.tag_configure('completed', background='#d4edda', foreground='#155724')
generated_data_tree_pg.tag_configure('error', background='#f8d7da', foreground='#721c24')

# Context menu for prompt generator treeview
def show_context_menu_pg(event):
    """Show context menu for prompt generator treeview."""
    # Select the item under cursor
    item = generated_data_tree_pg.identify_row(event.y)
    if item:
        generated_data_tree_pg.selection_set(item)

        # Create context menu
        context_menu_pg = tk.Menu(root, tearoff=0)
        context_menu_pg.add_command(label="📋 Copy Prompt", command=copy_selected_prompt_pg)
        context_menu_pg.add_command(label="📝 Copy Filename", command=copy_selected_filename_pg)
        context_menu_pg.add_separator()
        context_menu_pg.add_command(label="🗑️ Remove Entry", command=remove_selected_entry_pg)

        # Show context menu
        try:
            context_menu_pg.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu_pg.grab_release()

def copy_selected_prompt_pg():
    """Copy the selected prompt to clipboard."""
    selected_item = generated_data_tree_pg.selection()
    if selected_item:
        values = generated_data_tree_pg.item(selected_item[0], "values")
        if values and len(values) >= 3:
            prompt_text = values[2]  # Description column
            root.clipboard_clear()
            root.clipboard_append(prompt_text)
            log_activity_pg(f"Copied prompt to clipboard: {prompt_text[:50]}...")
            # Show temporary feedback
            show_copy_feedback_pg("Prompt copied to clipboard!")

def copy_selected_filename_pg():
    """Copy the selected filename to clipboard."""
    selected_item = generated_data_tree_pg.selection()
    if selected_item:
        values = generated_data_tree_pg.item(selected_item[0], "values")
        if values and len(values) >= 1:
            filename = values[0]  # Filename column
            root.clipboard_clear()
            root.clipboard_append(filename)
            log_activity_pg(f"Copied filename to clipboard: {filename}")
            show_copy_feedback_pg("Filename copied to clipboard!")

def remove_selected_entry_pg():
    """Remove the selected entry from the treeview."""
    selected_item = generated_data_tree_pg.selection()
    if selected_item:
        values = generated_data_tree_pg.item(selected_item[0], "values")
        if values:
            filename = values[0]
            generated_data_tree_pg.delete(selected_item[0])
            log_activity_pg(f"Removed entry: {filename}")
            show_copy_feedback_pg("Entry removed!")

def show_copy_feedback_pg(message):
    """Show temporary feedback message."""
    if 'status_label_pg' in globals():
        original_text = status_label_pg.cget("text")
        status_label_pg.config(text=message, foreground="green")
        # Reset after 2 seconds
        root.after(2000, lambda: status_label_pg.config(text=original_text, foreground="#666666"))

# Handle treeview clicks for copy buttons
def on_treeview_click_pg(event):
    """Handle clicks on the treeview, especially for copy buttons."""
    region = generated_data_tree_pg.identify_region(event.x, event.y)
    if region == "cell":
        column = generated_data_tree_pg.identify_column(event.x)
        item = generated_data_tree_pg.identify_row(event.y)

        # If clicked on Action column (column #4), copy the prompt
        if column == "#4" and item:
            copy_selected_prompt_pg()

# Bind events to the prompt generator treeview
generated_data_tree_pg.bind("<Button-3>", show_context_menu_pg)  # Right-click
generated_data_tree_pg.bind("<Button-1>", on_treeview_click_pg)  # Left-click for copy buttons

# Progress Label
progress_frame_pg = ttk.Frame(prompt_generator_frame)
progress_frame_pg.pack(fill=tk.X, pady=5, padx=10)
progress_label_pg = ttk.Label(progress_frame_pg, text="Ready", font=("Helvetica", 10))
progress_label_pg.pack(side=tk.LEFT, padx=5)

# Bottom Statistics and License Information
bottom_frame_pg = ttk.Frame(prompt_generator_frame)
bottom_frame_pg.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=(5, 10))

# Create the bottom info frame with dark background
bottom_info_frame_pg = tk.Frame(bottom_frame_pg, bg="#2c3e50", height=35)
bottom_info_frame_pg.pack(fill=tk.X)
bottom_info_frame_pg.pack_propagate(False)

# Generation Statistics (Left side)
stats_frame_pg = tk.Frame(bottom_info_frame_pg, bg="#2c3e50")
stats_frame_pg.pack(side=tk.LEFT, fill=tk.Y, padx=(10, 0))

# Initialize prompt generation counter
prompt_generation_count = 0

def update_prompt_stats():
    """Update the prompt generation statistics display."""
    global prompt_generation_count
    stats_text = f"📊 Generation Statistics: {prompt_generation_count} Prompts Generated"
    if 'stats_label_pg' in globals():
        stats_label_pg.config(text=stats_text)

stats_label_pg = tk.Label(stats_frame_pg, text="📊 Generation Statistics: 0 Prompts Generated",
                         font=("Segoe UI", 9), fg="white", bg="#2c3e50")
stats_label_pg.pack(side=tk.LEFT, pady=8)

# License Information (Right side)
license_frame_pg = tk.Frame(bottom_info_frame_pg, bg="#2c3e50")
license_frame_pg.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10))

# Get license info from main application
def get_license_info_pg():
    """Get license information for display using Meta Master license system."""
    try:
        # Use the existing Meta Master license system
        status, message, is_valid = get_license_info()

        # Extract useful information from the status and message
        if "License Active" in status:
            # Extract days from message like "🔹 Expires on: 2024-12-31 (30 days left)"
            if "days left" in message:
                import re
                days_match = re.search(r'\((\d+) days left\)', message)
                if days_match:
                    days = days_match.group(1)
                    return f"🔑 License Information: Expires in {days} days"
                else:
                    return "🔑 License Information: Active"
            else:
                return "🔑 License Information: Active"
        elif "License Expiring Soon" in status:
            # Extract days from message
            if "days left" in message:
                import re
                days_match = re.search(r'(\d+) days left', message)
                if days_match:
                    days = days_match.group(1)
                    return f"🔑 License Information: ⚠️ Expires in {days} days"
                else:
                    return "🔑 License Information: ⚠️ Expiring Soon"
            else:
                return "🔑 License Information: ⚠️ Expiring Soon"
        elif "License Expired" in status:
            return "🔑 License Information: ❌ Expired"
        elif "License Deactivated" in status:
            return "🔑 License Information: ❌ Deactivated"
        elif "Invalid License" in status:
            return "🔑 License Information: ❌ Invalid"
        elif "No License Found" in status:
            return "🔑 License Information: ❌ No License"
        else:
            return "🔑 License Information: Unknown Status"
    except:
        return "🔑 License Information: Error"

license_label_pg = tk.Label(license_frame_pg, text=get_license_info_pg(),
                           font=("Segoe UI", 9), fg="white", bg="#2c3e50")
license_label_pg.pack(side=tk.RIGHT, pady=8)

# Function to update license display periodically
def update_license_display_pg():
    """Update license display information."""
    if 'license_label_pg' in globals():
        license_label_pg.config(text=get_license_info_pg())
    # Schedule next update in 60 seconds
    root.after(60000, update_license_display_pg)

# Start license display updates
update_license_display_pg()

# Initialize database for prompt generator
def init_prompt_db():
    """Initialize the database and clear previous session data."""
    with sqlite3.connect("image_responses.db") as conn:
        cursor = conn.cursor()
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS responses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            image_url TEXT,
            description TEXT
        )
        """)
        # Clear previous session data
        cursor.execute("DELETE FROM responses")
        conn.commit()

def save_to_prompt_database(image_url, description):
    """Save a generated prompt to the database."""
    with sqlite3.connect("image_responses.db") as conn:
        cursor = conn.cursor()
        cursor.execute("INSERT INTO responses (image_url, description) VALUES (?, ?)", (image_url, description))
        conn.commit()

def log_activity_pg(msg):
    """Log activity to the prompt generator (simplified - no UI log)."""
    # Activity logging simplified - just print for debugging if needed
    # print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {msg}")
    pass

def apply_prompt_enhancements_pg(description):
    """Apply enhancement options to the generated prompt."""
    enhanced_description = description

    # Apply quality enhancements
    if quality_var_pg.get():
        quality_terms = ["masterpiece", "best quality", "ultra detailed", "high resolution", "professional"]
        enhanced_description = f"{', '.join(quality_terms)}, {enhanced_description}"

    # Apply cinematic enhancements
    if cinematic_var_pg.get():
        cinematic_terms = ["cinematic lighting", "dramatic composition", "film grain", "depth of field", "bokeh"]
        enhanced_description = f"{enhanced_description}, {', '.join(cinematic_terms)}"

    # Apply professional photography enhancements
    if photo_var_pg.get():
        photo_terms = ["professional photography", "studio lighting", "sharp focus", "award-winning", "commercial quality"]
        enhanced_description = f"{enhanced_description}, {', '.join(photo_terms)}"

    # Platform-specific formatting removed - not needed

    # Apply MidJourney formatting if enabled
    if mj_format_var_pg.get():
        enhanced_description = f"{enhanced_description} --v 6 --style raw --ar 16:9 --quality 2"

    # Apply word limit
    max_words = max_words_var_pg.get()
    words = enhanced_description.split()
    if len(words) > max_words:
        enhanced_description = " ".join(words[:max_words])

    return enhanced_description

def generate_prompt_pg(image_bytes, image_identifier):
    """Generate prompt using Gemini API for prompt generator mode."""
    prompt_mode = mode_var_pg.get()
    custom_prompt = custom_prompt_entry_pg.get().strip()

    # Enhanced prompt templates
    enhanced_prompts = {
        # Image-Specific Analysis Modes
        "Image Analysis": """Analyze this image and describe exactly what you see. Focus on: the main subject or objects in the image; their colors, shapes, and sizes; the background and setting; any text or symbols visible; the overall composition and layout; lighting conditions; style (photograph, illustration, digital art, etc.); any people, animals, or objects and their actions or positions. Be factual and specific about what is actually visible in the image. Avoid interpretation or assumptions - just describe the visual elements you can observe.""",

        "Vector Description": """Analyze this vector graphic and describe: the main design elements and their geometric properties; color palette used (specific colors, gradients, or patterns); line weights, curves, and shapes; scalability indicators (clean edges, simple forms); style characteristics (flat design, minimalist, detailed); any icons, symbols, or text elements; composition and balance; intended use or application; technical quality (resolution independence, clean paths). Focus on the vector-specific qualities that make this suitable for scaling and professional use.""",

        "Illustration Mode": """Describe this illustration focusing on: artistic style and technique (digital painting, hand-drawn, mixed media); color scheme and palette choices; composition and visual hierarchy; character design or subject matter; background elements and environment; artistic influences or style references; mood and atmosphere conveyed; level of detail and rendering quality; intended audience or purpose; unique artistic elements that make this illustration distinctive. Analyze both the technical execution and creative aspects.""",

        "Icon Description": """Analyze this icon and describe: the symbolic meaning and purpose; design style (flat, outlined, filled, gradient); size and scalability considerations; color usage and significance; geometric properties and proportions; clarity and recognizability at small sizes; consistency with icon design principles; intended use context (app, web, print); visual metaphors employed; accessibility and universal understanding. Focus on the functional design aspects that make this an effective icon.""",

        "Vector Set": """Describe this collection of vector graphics including: the unifying theme or concept; consistent design style across elements; color palette coordination; size relationships and proportions; intended use or application; style consistency (line weights, corner radius, visual treatment); completeness of the set; scalability and technical quality; target audience or market; how elements work together as a cohesive collection. Analyze both individual elements and the set as a whole.""",

        "Icon Set": """Analyze this icon set focusing on: the overall theme or category; design consistency across all icons; style uniformity (outline weight, corner radius, visual treatment); color scheme and usage; size and proportion relationships; symbolic clarity and recognition; intended platform or use case; completeness and coverage of the icon family; accessibility and universal understanding; technical quality and scalability. Evaluate how well the icons work together as a unified system.""",

        "Isolated Background": """Describe this isolated object or subject: the main subject and its key characteristics; background type (white, transparent, gradient); edge quality and cutout precision; lighting and shadow treatment; object positioning and orientation; material properties and textures; color accuracy and saturation; intended use (product catalog, stock photo, design element); technical quality of isolation; suitability for various applications. Focus on the professional isolation quality and commercial usability.""",

        "Technology Images": """Analyze this technology-related image describing: the specific technology or device shown; technical specifications visible; design and build quality; user interface elements; connectivity options; brand and model information; intended use case or application; target user demographic; innovation or unique features; technical complexity level; professional or consumer orientation; current technology trends reflected. Focus on the technical aspects and technological significance.""",

        "Ultra Descriptive": """Generate a hyperrealistic, cinematic prompt for image generation with precise visual storytelling. Analyze this image and create a perfect prompt including: subject's exact attire, physical posture, and positioning; facial expressions and hand gestures with close-up precision; interactive objects with material textures (metal, fabric, glass, wood); environmental elements (blurred/sharp backgrounds); realistic lighting type (soft natural, studio, diffused, golden hour); depth of field focus points; color palette and mood atmosphere; camera angle and composition. Use detailed, structured sentences. Ensure the scene is vivid enough to be recreated pixel-by-pixel. No generic terms. Do not start with 'Here's a description'. Format as a single, comprehensive prompt.""",

        "Creative Storytelling": """You are a master storyteller and visual poet. Transform this image into an enchanting narrative prompt that captures the soul of the scene. Describe it as if it's a pivotal moment in an epic tale, including: the emotional journey of subjects; hidden symbolism and deeper meanings; magical or surreal qualities that transcend reality; sensory details (sounds, scents, textures); the story that led to this moment; what might happen next. Use evocative, poetic language that stirs imagination. Avoid clichés like 'beautiful' or 'stunning'. Create a prompt that would inspire an artist to paint their masterpiece.""",

        "Technical Analysis": """Analyze this image from a professional photographer's perspective and create a technical prompt for recreation. Include: camera settings and equipment used (focal length, aperture, ISO); lighting setup (key light, fill light, rim light positions); composition techniques (rule of thirds, leading lines, symmetry); post-processing effects (color grading, contrast, saturation); depth of field characteristics; white balance and color temperature; shooting angle and perspective; technical challenges overcome. Format as a comprehensive technical brief for recreating this exact shot.""",

        "Marketing Copy": """Create a powerful marketing prompt that sells the emotion and lifestyle this image represents. Focus on: target audience demographics and psychographics; emotional triggers and desires fulfilled; lifestyle aspirations conveyed; brand values communicated; call-to-action potential; social media engagement factors; commercial applications; market positioning. Write as if creating a brief for a premium advertising campaign. Emphasize benefits over features, emotions over descriptions.""",

        "MidJourney Pro": """Generate an advanced MidJourney prompt with professional parameters. Structure: [Main subject and action] in [detailed environment], [artistic style] photography, [lighting description], [camera and lens details], [color palette], [mood and atmosphere], [composition notes]. Include technical parameters: --v 6 --style raw --ar 16:9 --chaos 10 --stylize 750. Add relevant style references like 'shot on Hasselblad H6D-400c', 'Zeiss Otus 85mm', 'cinematic lighting', 'hyperrealistic', 'award-winning photography'. Optimize for maximum visual impact.""",

        "DALL-E 3 Optimized": """Create a DALL-E 3 optimized prompt focusing on clarity and precision. Structure the prompt with: clear subject identification; specific artistic medium (photography, digital art, painting); detailed style descriptors; precise color specifications; lighting conditions; composition elements; quality indicators. Use DALL-E 3 friendly terms like 'photorealistic', 'high resolution', 'professional photography', 'studio lighting'. Avoid complex artistic jargon. Keep descriptions clear and unambiguous for best AI interpretation.""",

        "Stable Diffusion": """Generate a Stable Diffusion prompt with optimal token usage and weighting. Include: primary subject with (emphasis weights); artistic style and medium; quality boosters (masterpiece, best quality, ultra detailed); negative prompt suggestions; recommended sampling steps and CFG scale. Structure: (main subject:1.3), (style:1.2), (quality terms:1.1), detailed environment, professional lighting, (specific camera or art style:1.2). Suggest negative prompts for common issues. Optimize for photorealism and detail.""",

        "Product Photography": """Create a professional product photography prompt for commercial use. Specify: product positioning and angle; background type (seamless white, gradient, lifestyle); lighting setup (softbox, ring light, natural); shadow characteristics; reflection and highlight details; material textures and finishes; brand presentation elements; commercial photography standards; e-commerce optimization; catalog-ready specifications. Format for professional product photographers and commercial studios.""",

        "Artistic Vision": """Transform this image into an artistic masterpiece prompt. Include: artistic movement or style (impressionist, surrealist, contemporary); medium specifications (oil painting, watercolor, digital art); color theory application; emotional resonance; artistic techniques; compositional elements; texture and brushwork details; gallery-worthy presentation; artistic interpretation beyond literal representation. Create a prompt that would inspire fine artists and digital creators.""",

        "Cinematic Scene": """Develop a cinematic prompt as if directing a film scene. Include: camera movement and angles (dolly, crane, handheld); cinematic lighting (three-point, dramatic, natural); scene composition and framing; character blocking and positioning; mood and atmosphere; color grading style; film genre influences; director's vision; production value indicators. Format as a director's brief for recreating this scene with professional film equipment.""",

        "Character Design": """Create a character design prompt for game development or animation. Detail: character's personality traits reflected in appearance; clothing and accessory significance; facial features and expressions; body language and posture; character backstory elements; design functionality; animation considerations; style consistency; target audience appeal; memorable design elements. Format for concept artists and character designers.""",

        "Environment Design": """Generate an environment design prompt for world-building. Include: architectural elements and structures; environmental storytelling details; atmospheric conditions; scale and perspective; interactive elements; cultural or historical influences; functional design aspects; mood and ambiance; lighting and weather; ecosystem details. Perfect for game designers, architects, and environmental artists.""",

        "Stock Photo": """Create a commercial stock photography prompt optimized for licensing. Focus on: broad commercial appeal; diverse usage applications; clear subject matter; professional quality standards; keyword optimization; market demand factors; licensing potential; editorial vs commercial use; seasonal relevance; demographic appeal. Format for stock photographers and commercial image libraries.""",

        "Social Media": """Develop a social media optimized prompt for viral content creation. Include: platform-specific requirements (Instagram, TikTok, Pinterest); engagement factors; trending elements; hashtag potential; shareability factors; mobile optimization; attention-grabbing elements; brand-safe content; influencer appeal; user-generated content style. Perfect for social media managers and content creators.""",

        "E-commerce": """Generate an e-commerce product prompt for online retail. Specify: product presentation standards; conversion optimization elements; trust-building visuals; feature highlighting; lifestyle context; size and scale references; quality assurance indicators; mobile shopping optimization; comparison facilitation; purchase decision factors. Ideal for online retailers and marketplace sellers."""
    }

    # Use custom prompt if entered, otherwise select from predefined modes
    prompt_text = custom_prompt if custom_prompt else enhanced_prompts.get(prompt_mode, enhanced_prompts["Ultra Descriptive"])

    # Truncate if exceeds limit
    MAX_PROMPT_LENGTH = 1000
    if len(prompt_text) > MAX_PROMPT_LENGTH:
        prompt_text = prompt_text[:MAX_PROMPT_LENGTH]
        log_activity_pg(f"Prompt was truncated to {MAX_PROMPT_LENGTH} characters to fit within the limit.")

    try:
        # Use the main API key system from metadata generator
        api_key = load_gemini_api_key()
        if not api_key:
            log_activity_pg("Error: No API key found. Please add API keys in the main settings.")
            return

        genai.configure(api_key=api_key)
        model = genai.GenerativeModel("gemini-2.5-flash-lite")
        gemini_response = model.generate_content([
            {"inline_data": {"mime_type": "image/jpeg", "data": image_bytes}},
            {"text": f"{prompt_text} Limit the response to {max_words_var_pg.get()} words."}
        ])

        # Clean up Gemini's response
        description = gemini_response.text.strip()
        unwanted_intro_phrases = [
            "Here's a description of the image", "This is a description of the image", "suitable for AI image generation:",
            "Image Description:", "Here's a structured description for AI image generation based on the image of",
            "The image is a", "This image shows a", "The image depicts a", "The image opens on a", "The scene unfolds in a",
        ]
        for phrase in unwanted_intro_phrases:
            if description.startswith(phrase):
                description = description[len(phrase):].strip()

        # Ensure the prompt starts with "Generate a"
        if not description.lower().startswith("generate a"):
            description = f"Generate a Image of {description}"

        # Format the description as a single paragraph
        description = " ".join(description.split())

        # Apply enhancement options
        description = apply_prompt_enhancements_pg(description)

        # Save and update UI
        save_to_prompt_database(image_identifier, description)
        root.after(0, lambda: generated_data_tree_pg.insert('', tk.END, values=(image_identifier, prompt_mode, description, "📋 Copy")))
        root.after(0, lambda: log_activity_pg(f"{prompt_mode} mode processed: {image_identifier}"))

        # Increment prompt generation counter and update stats
        global prompt_generation_count
        prompt_generation_count += 1
        root.after(0, update_prompt_stats)

    except Exception as e:
        root.after(0, lambda err=str(e): log_activity_pg(f"Error generating prompt: {err}"))

def process_local_image_pg(image_path, retries=3):
    """Process a single local image with retry logic."""
    for attempt in range(retries):
        try:
            with open(image_path, "rb") as img_file:
                image_bytes = base64.b64encode(img_file.read()).decode("utf-8")
            generate_prompt_pg(image_bytes, image_path)
            return True
        except Exception as e:
            log_activity_pg(f"Attempt {attempt + 1} failed for {image_path}: {e}")
            time.sleep(1)
    log_activity_pg(f"Failed to process {image_path} after {retries} attempts.")
    return False

def process_local_image_optimized_pg(image_path, index, total):
    """Optimized processing for local images with better error handling and UI updates."""
    filename = os.path.basename(image_path)

    try:
        # Update UI to show current processing status
        root.after(0, lambda: update_treeview_status_pg(filename, "Processing..."))

        # Optimize image loading and encoding
        with open(image_path, "rb") as img_file:
            # Read file in chunks for better memory management
            file_content = img_file.read()

        # Check file size and optimize if needed
        if len(file_content) > 10 * 1024 * 1024:  # 10MB limit
            log_activity_pg(f"Large file detected: {filename}. Optimizing...")
            # For large files, resize before encoding
            from PIL import Image
            import io

            img = Image.open(io.BytesIO(file_content))
            # Resize if too large (max 2048x2048 for faster processing)
            if img.width > 2048 or img.height > 2048:
                img.thumbnail((2048, 2048), Image.Resampling.LANCZOS)

            # Convert to JPEG with quality optimization
            output = io.BytesIO()
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            img.save(output, format='JPEG', quality=85, optimize=True)
            file_content = output.getvalue()

        # Encode to base64
        image_bytes = base64.b64encode(file_content).decode("utf-8")

        # Generate prompt with optimized function
        generate_prompt_optimized_pg(image_bytes, image_path, filename)

        return True

    except Exception as e:
        error_msg = f"Failed to process {filename}: {str(e)}"
        log_activity_pg(error_msg)
        root.after(0, lambda: update_treeview_status_pg(filename, f"Error: {str(e)[:50]}..."))
        return False

def update_treeview_status_pg(filename, status):
    """Update the status of a file in the treeview."""
    for item in generated_data_tree_pg.get_children():
        values = generated_data_tree_pg.item(item)['values']
        if values and values[0] == filename:
            # Determine action button based on status
            action_text = "📋 Copy" if "processed" in status.lower() or "generated" in status.lower() else "⏳ Wait"
            generated_data_tree_pg.item(item, values=(filename, values[1] if len(values) > 1 else "Processing", status, action_text))
            break

def generate_prompt_optimized_pg(image_bytes, image_path, filename):
    """Optimized prompt generation with better UI feedback."""
    prompt_mode = mode_var_pg.get()
    custom_prompt = custom_prompt_entry_pg.get().strip()

    # Use the same enhanced prompts but with optimized processing
    enhanced_prompts = {
        # Image-Specific Analysis Modes
        "Image Analysis": """Analyze this image and describe exactly what you see. Focus on: the main subject or objects in the image; their colors, shapes, and sizes; the background and setting; any text or symbols visible; the overall composition and layout; lighting conditions; style (photograph, illustration, digital art, etc.); any people, animals, or objects and their actions or positions. Be factual and specific about what is actually visible in the image. Avoid interpretation or assumptions - just describe the visual elements you can observe.""",

        "Vector Description": """Analyze this vector graphic and describe: the main design elements and their geometric properties; color palette used (specific colors, gradients, or patterns); line weights, curves, and shapes; scalability indicators (clean edges, simple forms); style characteristics (flat design, minimalist, detailed); any icons, symbols, or text elements; composition and balance; intended use or application; technical quality (resolution independence, clean paths). Focus on the vector-specific qualities that make this suitable for scaling and professional use.""",

        "Illustration Mode": """Describe this illustration focusing on: artistic style and technique (digital painting, hand-drawn, mixed media); color scheme and palette choices; composition and visual hierarchy; character design or subject matter; background elements and environment; artistic influences or style references; mood and atmosphere conveyed; level of detail and rendering quality; intended audience or purpose; unique artistic elements that make this illustration distinctive. Analyze both the technical execution and creative aspects.""",

        "Icon Description": """Analyze this icon and describe: the symbolic meaning and purpose; design style (flat, outlined, filled, gradient); size and scalability considerations; color usage and significance; geometric properties and proportions; clarity and recognizability at small sizes; consistency with icon design principles; intended use context (app, web, print); visual metaphors employed; accessibility and universal understanding. Focus on the functional design aspects that make this an effective icon.""",

        "Vector Set": """Describe this collection of vector graphics including: the unifying theme or concept; consistent design style across elements; color palette coordination; size relationships and proportions; intended use or application; style consistency (line weights, corner radius, visual treatment); completeness of the set; scalability and technical quality; target audience or market; how elements work together as a cohesive collection. Analyze both individual elements and the set as a whole.""",

        "Icon Set": """Analyze this icon set focusing on: the overall theme or category; design consistency across all icons; style uniformity (outline weight, corner radius, visual treatment); color scheme and usage; size and proportion relationships; symbolic clarity and recognition; intended platform or use case; completeness and coverage of the icon family; accessibility and universal understanding; technical quality and scalability. Evaluate how well the icons work together as a unified system.""",

        "Isolated Background": """Describe this isolated object or subject: the main subject and its key characteristics; background type (white, transparent, gradient); edge quality and cutout precision; lighting and shadow treatment; object positioning and orientation; material properties and textures; color accuracy and saturation; intended use (product catalog, stock photo, design element); technical quality of isolation; suitability for various applications. Focus on the professional isolation quality and commercial usability.""",

        "Technology Images": """Analyze this technology-related image describing: the specific technology or device shown; technical specifications visible; design and build quality; user interface elements; connectivity options; brand and model information; intended use case or application; target user demographic; innovation or unique features; technical complexity level; professional or consumer orientation; current technology trends reflected. Focus on the technical aspects and technological significance.""",

        "Ultra Descriptive": """Generate a hyperrealistic, cinematic prompt for image generation with precise visual storytelling. Analyze this image and create a perfect prompt including: subject's exact attire, physical posture, and positioning; facial expressions and hand gestures with close-up precision; interactive objects with material textures (metal, fabric, glass, wood); environmental elements (blurred/sharp backgrounds); realistic lighting type (soft natural, studio, diffused, golden hour); depth of field focus points; color palette and mood atmosphere; camera angle and composition. Use detailed, structured sentences. Ensure the scene is vivid enough to be recreated pixel-by-pixel. No generic terms. Do not start with 'Here's a description'. Format as a single, comprehensive prompt.""",
        "Creative Storytelling": """You are a master storyteller and visual poet. Transform this image into an enchanting narrative prompt that captures the soul of the scene. Describe it as if it's a pivotal moment in an epic tale, including: the emotional journey of subjects; hidden symbolism and deeper meanings; magical or surreal qualities that transcend reality; sensory details (sounds, scents, textures); the story that led to this moment; what might happen next. Use evocative, poetic language that stirs imagination. Avoid clichés like 'beautiful' or 'stunning'. Create a prompt that would inspire an artist to paint their masterpiece.""",
        "Technical Analysis": """Analyze this image from a professional photographer's perspective and create a technical prompt for recreation. Include: camera settings and equipment used (focal length, aperture, ISO); lighting setup (key light, fill light, rim light positions); composition techniques (rule of thirds, leading lines, symmetry); post-processing effects (color grading, contrast, saturation); depth of field characteristics; white balance and color temperature; shooting angle and perspective; technical challenges overcome. Format as a comprehensive technical brief for recreating this exact shot.""",
        "Marketing Copy": """Create a powerful marketing prompt that sells the emotion and lifestyle this image represents. Focus on: target audience demographics and psychographics; emotional triggers and desires fulfilled; lifestyle aspirations conveyed; brand values communicated; call-to-action potential; social media engagement factors; commercial applications; market positioning. Write as if creating a brief for a premium advertising campaign. Emphasize benefits over features, emotions over descriptions.""",
        "MidJourney Pro": """Generate an advanced MidJourney prompt with professional parameters. Structure: [Main subject and action] in [detailed environment], [artistic style] photography, [lighting description], [camera and lens details], [color palette], [mood and atmosphere], [composition notes]. Include technical parameters: --v 6 --style raw --ar 16:9 --chaos 10 --stylize 750. Add relevant style references like 'shot on Hasselblad H6D-400c', 'Zeiss Otus 85mm', 'cinematic lighting', 'hyperrealistic', 'award-winning photography'. Optimize for maximum visual impact.""",
        "DALL-E 3 Optimized": """Create a DALL-E 3 optimized prompt focusing on clarity and precision. Structure the prompt with: clear subject identification; specific artistic medium (photography, digital art, painting); detailed style descriptors; precise color specifications; lighting conditions; composition elements; quality indicators. Use DALL-E 3 friendly terms like 'photorealistic', 'high resolution', 'professional photography', 'studio lighting'. Avoid complex artistic jargon. Keep descriptions clear and unambiguous for best AI interpretation.""",
        "Stable Diffusion": """Generate a Stable Diffusion prompt with optimal token usage and weighting. Include: primary subject with (emphasis weights); artistic style and medium; quality boosters (masterpiece, best quality, ultra detailed); negative prompt suggestions; recommended sampling steps and CFG scale. Structure: (main subject:1.3), (style:1.2), (quality terms:1.1), detailed environment, professional lighting, (specific camera or art style:1.2). Suggest negative prompts for common issues. Optimize for photorealism and detail.""",
        "Product Photography": """Create a professional product photography prompt for commercial use. Specify: product positioning and angle; background type (seamless white, gradient, lifestyle); lighting setup (softbox, ring light, natural); shadow characteristics; reflection and highlight details; material textures and finishes; brand presentation elements; commercial photography standards; e-commerce optimization; catalog-ready specifications. Format for professional product photographers and commercial studios.""",
        "Artistic Vision": """Transform this image into an artistic masterpiece prompt. Include: artistic movement or style (impressionist, surrealist, contemporary); medium specifications (oil painting, watercolor, digital art); color theory application; emotional resonance; artistic techniques; compositional elements; texture and brushwork details; gallery-worthy presentation; artistic interpretation beyond literal representation. Create a prompt that would inspire fine artists and digital creators.""",
        "Cinematic Scene": """Develop a cinematic prompt as if directing a film scene. Include: camera movement and angles (dolly, crane, handheld); cinematic lighting (three-point, dramatic, natural); scene composition and framing; character blocking and positioning; mood and atmosphere; color grading style; film genre influences; director's vision; production value indicators. Format as a director's brief for recreating this scene with professional film equipment.""",
        "Character Design": """Create a character design prompt for game development or animation. Detail: character's personality traits reflected in appearance; clothing and accessory significance; facial features and expressions; body language and posture; character backstory elements; design functionality; animation considerations; style consistency; target audience appeal; memorable design elements. Format for concept artists and character designers.""",
        "Environment Design": """Generate an environment design prompt for world-building. Include: architectural elements and structures; environmental storytelling details; atmospheric conditions; scale and perspective; interactive elements; cultural or historical influences; functional design aspects; mood and ambiance; lighting and weather; ecosystem details. Perfect for game designers, architects, and environmental artists.""",
        "Stock Photo": """Create a commercial stock photography prompt optimized for licensing. Focus on: broad commercial appeal; diverse usage applications; clear subject matter; professional quality standards; keyword optimization; market demand factors; licensing potential; editorial vs commercial use; seasonal relevance; demographic appeal. Format for stock photographers and commercial image libraries.""",
        "Social Media": """Develop a social media optimized prompt for viral content creation. Include: platform-specific requirements (Instagram, TikTok, Pinterest); engagement factors; trending elements; hashtag potential; shareability factors; mobile optimization; attention-grabbing elements; brand-safe content; influencer appeal; user-generated content style. Perfect for social media managers and content creators.""",
        "E-commerce": """Generate an e-commerce product prompt for online retail. Specify: product presentation standards; conversion optimization elements; trust-building visuals; feature highlighting; lifestyle context; size and scale references; quality assurance indicators; mobile shopping optimization; comparison facilitation; purchase decision factors. Ideal for online retailers and marketplace sellers."""
    }

    prompt_text = custom_prompt if custom_prompt else enhanced_prompts.get(prompt_mode, enhanced_prompts["Ultra Descriptive"])

    try:
        # Use the main API key system
        api_key = load_gemini_api_key()
        if not api_key:
            root.after(0, lambda: update_treeview_status_pg(filename, "Error: No API key"))
            return

        genai.configure(api_key=api_key)
        model = genai.GenerativeModel("gemini-2.5-flash-lite")

        # Optimized API call with timeout
        gemini_response = model.generate_content([
            {"inline_data": {"mime_type": "image/jpeg", "data": image_bytes}},
            {"text": f"{prompt_text} Limit the response to {max_words_var_pg.get()} words."}
        ])

        # Process response
        description = gemini_response.text.strip()

        # Clean up response (same as before but optimized)
        unwanted_phrases = [
            "Here's a description of the image", "This is a description of the image",
            "suitable for AI image generation:", "Image Description:",
            "Here's a structured description for AI image generation based on the image of",
            "The image is a", "This image shows a", "The image depicts a"
        ]

        for phrase in unwanted_phrases:
            if description.startswith(phrase):
                description = description[len(phrase):].strip()
                break

        if not description.lower().startswith("generate a"):
            description = f"Generate a Image of {description}"

        description = " ".join(description.split())

        # Apply enhancement options
        description = apply_prompt_enhancements_pg(description)

        # Save to database
        save_to_prompt_database(image_path, description)

        # Update UI
        root.after(0, lambda: update_treeview_with_result_pg(filename, prompt_mode, description))
        root.after(0, lambda: log_activity_pg(f"✓ {prompt_mode} prompt generated for {filename}"))

    except Exception as e:
        error_msg = f"API Error: {str(e)}"
        root.after(0, lambda: update_treeview_status_pg(filename, error_msg))
        root.after(0, lambda: log_activity_pg(f"✗ Failed to generate prompt for {filename}: {str(e)}"))

def update_treeview_with_result_pg(filename, mode, description):
    """Update treeview with the generated prompt result."""
    for item in generated_data_tree_pg.get_children():
        values = generated_data_tree_pg.item(item)['values']
        if values and values[0] == filename:
            generated_data_tree_pg.item(item, values=(filename, mode, description, "📋 Copy"))
            break

def process_image_url_pg(image_url, retries=3):
    """Process an image URL with retry logic."""
    for attempt in range(retries):
        try:
            response = requests.get(image_url, timeout=10)
            response.raise_for_status()
            image_bytes = base64.b64encode(response.content).decode("utf-8")
            generate_prompt_pg(image_bytes, image_url)
            return True
        except Exception as e:
            log_activity_pg(f"Attempt {attempt + 1} failed for {image_url}: {e}")
            time.sleep(1)
    log_activity_pg(f"Failed to process {image_url} after {retries} attempts.")
    return False

def monitor_clipboard_pg():
    global monitoring_pg
    last_clipboard = ""
    while monitoring_pg:
        try:
            clipboard_content = pyperclip.paste()
            if clipboard_content.startswith("http") and clipboard_content != last_clipboard:
                process_image_url_pg(clipboard_content)
                last_clipboard = clipboard_content
        except Exception as e:
            log_activity_pg(f"Clipboard monitoring error: {e}")
        time.sleep(2)

def start_monitoring_pg():
    global monitoring_pg
    monitoring_pg = True
    threading.Thread(target=monitor_clipboard_pg, daemon=True).start()
    log_activity_pg("Clipboard monitoring started.")

def stop_monitoring_pg():
    global monitoring_pg
    monitoring_pg = False
    log_activity_pg("Clipboard monitoring stopped.")
    messagebox.showinfo("Monitoring", "Clipboard monitoring stopped.")

def clear_work_pg():
    """Clear all work in the UI and database."""
    if 'generated_data_tree_pg' in globals():
        generated_data_tree_pg.delete(*generated_data_tree_pg.get_children())

    with sqlite3.connect("image_responses.db") as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM responses")
        conn.commit()

    log_activity_pg("Cleared all work and database.")

def export_to_csv_pg():
    """Export current session's data to a CSV file."""
    file_path = filedialog.asksaveasfilename(defaultextension=".csv", filetypes=[("CSV files", "*.csv")])
    if file_path:
        with sqlite3.connect("image_responses.db") as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM responses")
            rows = cursor.fetchall()

        if not rows:
            messagebox.showwarning("Export", "No data to export.")
            return

        with open(file_path, "w", newline="", encoding="utf-8") as file:
            writer = csv.writer(file)
            writer.writerow(["ID", "Image URL", "Description"])
            writer.writerows(rows)

        log_activity_pg(f"Exported data to {file_path}")
        messagebox.showinfo("Export", "Export completed successfully!")

def copy_prompt_pg():
    """Copy the selected prompt to clipboard."""
    if 'generated_data_tree_pg' in globals():
        selection = generated_data_tree_pg.selection()
        if selection:
            item = generated_data_tree_pg.item(selection[0])
            values = item['values']
            description = values[2] if len(values) > 2 else ""
            if description and description != "Ready for processing...":
                pyperclip.copy(description)
                log_activity_pg(f"Prompt copied to clipboard: {description[:50]}...")
                show_copy_feedback_pg("Prompt copied to clipboard!")
            else:
                show_copy_feedback_pg("No prompt available to copy.")
        else:
            show_copy_feedback_pg("Please select a prompt to copy.")

def select_local_images_pg():
    global local_images_pg
    local_images_pg = filedialog.askopenfilenames(filetypes=[("Image Files", "*.jpg;*.jpeg;*.png")])

    # Add selected files to the treeview with "Pending" status
    for file in local_images_pg:
        filename = os.path.basename(file)
        generated_data_tree_pg.insert('', tk.END, values=(filename, "Pending", "Ready for processing...", "⏳ Wait"))

    log_activity_pg(f"Selected {len(local_images_pg)} local images.")

def start_processing_local_images_pg():
    """Process all selected local images with optimized parallel processing."""
    if not local_images_pg:
        messagebox.showwarning("No Files", "Please select local images first.")
        return

    def process_all_images():
        total_images = len(local_images_pg)
        log_activity_pg(f"Starting to process {total_images} local images with optimized processing...")

        progress_label_pg.config(text=f"Processing: 0/{total_images}")
        root.update()

        successful = 0
        failed = 0

        # Process images with optimized batch processing
        for index, path in enumerate(local_images_pg, start=1):
            try:
                # Update progress immediately
                progress_label_pg.config(text=f"Processing: {index}/{total_images} - {os.path.basename(path)}")
                root.update()

                # Process with optimized function
                if process_local_image_optimized_pg(path, index, total_images):
                    successful += 1
                else:
                    failed += 1

                # Update progress after each file
                progress_label_pg.config(text=f"Processing: {index}/{total_images} (✓{successful} ✗{failed})")
                root.update()

            except Exception as e:
                failed += 1
                log_activity_pg(f"Error processing {os.path.basename(path)}: {str(e)}")

        log_activity_pg(f"Finished processing. Success: {successful}, Failed: {failed}")
        progress_label_pg.config(text=f"Completed: ✓{successful} ✗{failed} of {total_images}")

    threading.Thread(target=process_all_images, daemon=True).start()

# Create the professional action buttons after function definitions
select_images_btn_pg = ttk.Button(file_section_pg, text="📁 Select Images", command=select_local_images_pg,
                                 bootstyle="primary", width=15)
select_images_btn_pg.pack(side=tk.LEFT, padx=(0, 10))

start_monitoring_btn_pg = ttk.Button(process_section_pg, text="▶️ Start Monitoring", command=start_monitoring_pg,
                                    bootstyle="success", width=18)
start_monitoring_btn_pg.pack(side=tk.LEFT, padx=(0, 5))

stop_monitoring_btn_pg = ttk.Button(process_section_pg, text="⏹️ Stop Monitoring", command=stop_monitoring_pg,
                                   bootstyle="danger", width=18)
stop_monitoring_btn_pg.pack(side=tk.LEFT, padx=(0, 5))

process_local_btn_pg = ttk.Button(process_section_pg, text="🚀 Process Images", command=start_processing_local_images_pg,
                                 bootstyle="primary", width=15)
process_local_btn_pg.pack(side=tk.LEFT, padx=(0, 10))

export_csv_btn_pg = ttk.Button(utility_section_pg, text="📊 Export CSV", command=export_to_csv_pg,
                              bootstyle="info", width=12)
export_csv_btn_pg.pack(side=tk.LEFT, padx=(0, 5))

copy_prompt_btn_pg = ttk.Button(utility_section_pg, text="📋 Copy Prompt", command=copy_prompt_pg,
                               bootstyle="secondary", width=12)
copy_prompt_btn_pg.pack(side=tk.LEFT, padx=(0, 5))

clear_work_btn_pg = ttk.Button(utility_section_pg, text="🗑️ Clear All", command=clear_work_pg,
                              bootstyle="warning", width=12)
clear_work_btn_pg.pack(side=tk.LEFT)

# Initialize the prompt generator database
init_prompt_db()

root.mainloop()
